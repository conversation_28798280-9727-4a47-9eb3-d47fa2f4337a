"""
Utilitário para fazer requisições a APIs externas
"""
import requests
import os
from dotenv import load_dotenv
import logging
from .cnae_mapper import get_activity_and_destination

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Carregar variáveis de ambiente
load_dotenv()

def fetch_cnpj_data(cnpj):
    """
    Busca dados de um CNPJ na API CNPJ.ws

    Args:
        cnpj (str): CNPJ a ser consultado (apenas números)

    Returns:
        dict: Dados do CNPJ ou None em caso de erro
    """
    # Remover caracteres não numéricos do CNPJ
    cnpj = ''.join(filter(str.isdigit, cnpj))

    if not cnpj or len(cnpj) != 14:
        logger.error(f"CNPJ inválido: {cnpj}")
        return None

    # Token da API (deve ser configurado no .env)
    token = os.getenv('CNPJ_WS_TOKEN', 'DAsEVpQfzFs0egLMxAYORJdnlKqVE9u9HBgdyxpUB7c7')

    if not token:
        logger.error("Token da API CNPJ.ws não configurado")
        return None

    # URL da API
    url = f"https://comercial.cnpj.ws/cnpj/{cnpj}?token={token}"

    try:
        logger.info(f"Consultando CNPJ {cnpj} na API CNPJ.ws")
        response = requests.get(url, timeout=15)  # Aumentar timeout para 15 segundos

        # Verificar se a requisição foi bem-sucedida
        if response.status_code == 200:
            try:
                data = response.json()
                logger.info(f"Resposta da API para CNPJ {cnpj}: {data}")

                # Extrair apenas os dados necessários
                result = {
                    'cnae': None,
                    'simples_nacional': False,
                    'atividade': None,
                    'destinacao': None,
                    'divisao_cnae': None
                }

                # Verificar se a resposta contém os dados esperados
                if 'estabelecimento' not in data:
                    logger.error(f"Resposta da API não contém dados de estabelecimento para CNPJ {cnpj}")
                    logger.error(f"Resposta completa: {data}")
                    return result  # Retornar o resultado vazio em vez de None

                # Extrair CNAE com verificações de segurança
                if ('estabelecimento' in data and
                    data['estabelecimento'] is not None and
                    'atividade_principal' in data['estabelecimento'] and
                    data['estabelecimento']['atividade_principal'] is not None and
                    'id' in data['estabelecimento']['atividade_principal']):

                    cnae_code = data['estabelecimento']['atividade_principal']['id']
                    result['cnae'] = cnae_code

                    # Extrair divisão do CNAE (primeiros 2 dígitos)
                    if cnae_code and len(cnae_code) >= 2:
                        result['divisao_cnae'] = cnae_code[:2]

                        # Obter atividade e destinação sugeridas
                        atividade, destinacao = get_activity_and_destination(cnae_code)
                        result['atividade'] = atividade
                        result['destinacao'] = destinacao

                        logger.info(f"CNAE {cnae_code} mapeado para Atividade: {atividade}, Destinação: {destinacao}")

                # Extrair status do Simples Nacional com verificações de segurança
                if ('simples' in data and
                    data['simples'] is not None and
                    'simples' in data['simples']):
                    result['simples_nacional'] = data['simples']['simples'] == 'Sim'

            except ValueError as e:
                logger.error(f"Erro ao processar JSON da resposta para CNPJ {cnpj}: {str(e)}")
                logger.error(f"Conteúdo da resposta: {response.text}")
                # Retornar um resultado vazio em vez de None
                return {
                    'cnae': None,
                    'simples_nacional': False,
                    'atividade': None,
                    'destinacao': None,
                    'divisao_cnae': None
                }

            logger.info(f"Dados do CNPJ {cnpj} obtidos com sucesso: {result}")
            return result
        else:
            logger.error(f"Erro ao consultar CNPJ {cnpj}: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"Erro ao consultar CNPJ {cnpj}: {str(e)}")
        return None
