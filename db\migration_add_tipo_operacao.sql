-- Migration to add tipo_operacao column to tributo table
-- This column will store the value from the tpNF tag (0 for entrada, 1 for saída)

-- Add the column to the tributo table
ALTER TABLE tributo ADD COLUMN tipo_operacao VARCHAR(1);

-- Add a comment to explain the column
COMMENT ON COLUMN tributo.tipo_operacao IS 'Tipo de operação: 0 = entrada, 1 = saída (valor da tag tpNF do XML)';

-- Create an index for better performance when filtering by tipo_operacao
CREATE INDEX IF NOT EXISTS idx_tributo_tipo_operacao ON tributo(tipo_operacao);

-- Update the existing data (if any) based on data_saida
-- If data_saida is NULL, it's an entrada (0)
-- If data_saida is NOT NULL, it's a saída (1)
UPDATE tributo SET tipo_operacao = CASE WHEN data_saida IS NULL THEN '0' ELSE '1' END;
