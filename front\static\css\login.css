/*
 * Login CSS - Auditoria Fiscal
 * Estilos específicos para a página de login
 */

/* Layout principal */
.login-page {
    min-height: 100vh;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
    padding: 2rem 1rem;
}

body.dark-theme .login-page {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Formas decorativas de fundo */
.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    opacity: 0.1;
}

.shape-1 {
    width: 500px;
    height: 500px;
    top: -250px;
    left: -100px;
    animation: float 15s ease-in-out infinite;
}

.shape-2 {
    width: 400px;
    height: 400px;
    bottom: -200px;
    right: -100px;
    animation: float 18s ease-in-out infinite reverse;
}

.shape-3 {
    width: 300px;
    height: 300px;
    bottom: 30%;
    left: 10%;
    animation: float 20s ease-in-out infinite 2s;
}

.shape-4 {
    width: 200px;
    height: 200px;
    top: 20%;
    right: 10%;
    animation: float 12s ease-in-out infinite 1s;
}

@keyframes float {
    0% {
        transform: translate(0, 0) rotate(0deg);
    }
    50% {
        transform: translate(30px, 20px) rotate(5deg);
    }
    100% {
        transform: translate(0, 0) rotate(0deg);
    }
}

/* Container principal */
.login-container {
    width: 100%;
    max-width: 500px;
    z-index: 1;
    position: relative;
}

/* Card de login */
.login-card {
    background-color: #ffffff;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    animation: fadeInUp 0.8s ease-out;
}

body.dark-theme .login-card {
    background-color: #1e293b;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

/* Cabeçalho do card */
.login-header {
    padding: 2rem 2rem 1rem;
    text-align: center;
    position: relative;
}

.theme-toggle {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.theme-toggle .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
    border-radius: 50%;
    color: var(--gray-600);
    background-color: var(--gray-100);
    transition: all 0.3s ease;
}

.theme-toggle .btn i {
    transition: transform 0.3s ease;
}

.theme-toggle .btn:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
    transform: rotate(15deg);
}

body.dark-theme .theme-toggle .btn {
    color: var(--gray-300);
    background-color: var(--gray-700);
}

body.dark-theme .theme-toggle .btn:hover {
    background-color: var(--gray-600);
    color: var(--gray-100);
}

.login-logo-container {
    margin-bottom: 1.5rem;
}

.login-logo {
    max-height: 80px;
    transition: all 0.3s ease;
}

.login-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--gray-800);
}

.login-subtitle {
    color: var(--gray-600);
    font-size: 1rem;
}

body.dark-theme .login-title {
    color: var(--gray-200);
}

body.dark-theme .login-subtitle {
    color: var(--gray-400);
}

/* Corpo do card */
.login-body {
    padding: 1rem 2rem 2rem;
}

/* Formulário */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

body.dark-theme .form-label {
    color: var(--gray-300);
}

.input-group {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.input-group:focus-within {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

body.dark-theme .input-group {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

body.dark-theme .input-group:focus-within {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.input-group-text {
    background-color: #f8fafc;
    border-color: #e2e8f0;
    color: var(--gray-600);
}

.form-control {
    border-color: #e2e8f0;
    padding: 0.75rem 1rem;
    font-size: 1rem;
}

.form-control:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

body.dark-theme .input-group-text {
    background-color: #334155;
    border-color: #475569;
    color: var(--gray-300);
}

body.dark-theme .form-control {
    background-color: #1e293b;
    border-color: #475569;
    color: var(--gray-300);
}

body.dark-theme .form-control:focus {
    border-color: var(--primary-color);
}

.toggle-password {
    cursor: pointer;
    background-color: #f8fafc;
    border-color: #e2e8f0;
    color: var(--gray-600);
}

body.dark-theme .toggle-password {
    background-color: #334155;
    border-color: #475569;
    color: var(--gray-300);
}

.form-check-label {
    color: var(--gray-700);
    font-size: 0.9rem;
}

body.dark-theme .form-check-label {
    color: var(--gray-300);
}

.forgot-password {
    color: var(--primary-color);
    font-size: 0.9rem;
    transition: all 0.2s;
    text-decoration: none;
}

.forgot-password:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Botão de login */
.login-btn {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(59, 130, 246, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

body.dark-theme .login-btn {
    box-shadow: 0 4px 10px rgba(59, 130, 246, 0.2);
}

body.dark-theme .login-btn:hover {
    box-shadow: 0 6px 15px rgba(59, 130, 246, 0.3);
}

/* Rodapé do card */
.login-footer {
    padding: 1.5rem 2rem;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

body.dark-theme .login-footer {
    background-color: #0f172a;
    border-top: 1px solid #334155;
}

.features {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 0.5rem;
}

.feature-item {
    font-size: 0.85rem;
    color: var(--gray-600);
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.feature-item i {
    color: var(--primary-color);
    margin-right: 0.5rem;
    font-size: 0.9rem;
}

body.dark-theme .feature-item {
    color: var(--gray-400);
}

body.dark-theme .feature-item i {
    color: var(--primary-light);
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsividade */
@media (max-width: 576px) {
    .login-card {
        border-radius: 15px;
    }

    .login-header,
    .login-body {
        padding: 1.5rem 1.5rem;
    }

    .login-footer {
        padding: 1.2rem 1.5rem;
    }

    .login-logo {
        max-height: 60px;
    }

    .login-title {
        font-size: 1.5rem;
    }

    .features {
        flex-direction: column;
    }
}

/* Estilos para mensagens de erro/sucesso */
#login-msg.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border-left: 3px solid #ef4444;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-top: 1rem;
}

#login-msg.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border-left: 3px solid #10b981;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-top: 1rem;
}

body.dark-theme #login-msg.alert-danger {
    background-color: rgba(239, 68, 68, 0.15);
    color: #f87171;
}

body.dark-theme #login-msg.alert-success {
    background-color: rgba(16, 185, 129, 0.15);
    color: #34d399;
}
