/**
 * auditoria_dashboard.js - Funções para o dashboard de auditoria
 */

// Cache para dados de detalhamento e opções de filtros
let detalhamentoCache = new Map();
let filtroOptionsCache = new Map();
let debounceTimers = new Map();

// Mapa para armazenar os relacionamentos entre CFOP e NCM
const cfopNcmRelations = new Map();

/**
 * Carrega os relacionamentos entre CFOP e NCM do backend
 */
function carregarRelacionamentosCfopNcm() {
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  if (!empresaId) return Promise.resolve();

  const url = `http://127.0.0.1:5000/api/auditoria/relacionamentos/cfop-ncm?empresa_id=${empresaId}&year=${year}&month=${month}`;

  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success && data.relacionamentos) {
        // Limpar relacionamentos existentes
        cfopNcmRelations.clear();

        // Preencher o mapa de relacionamentos
        data.relacionamentos.forEach((rel) => {
          if (!cfopNcmRelations.has(rel.cfop)) {
            cfopNcmRelations.set(rel.cfop, new Set());
          }
          cfopNcmRelations.get(rel.cfop).add(rel.ncm);
        });

        return true;
      }
      return false;
    })
    .catch((error) => {
      return false;
    });
}

/**
 * Carrega os dados do dashboard de auditoria
 * @param {string} tipoTributo - Tipo de tributo (icms, icms_st, ipi, pis, cofins, difal)
 */
function carregarDashboardAuditoria(tipoTributo) {
  // Limpar cache ao carregar novo dashboard
  limparCacheDetalhamento();
  // Mostrar loading
  showLoading();

  // Obter empresa, ano e mês selecionados
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  if (!empresaId) {
    hideLoading();
    showErrorMessage(
      'Selecione uma empresa para visualizar o dashboard de auditoria.',
    );
    return;
  }

  // Construir URL com filtros
  const url = `http://127.0.0.1:5000/api/auditoria/dashboard?empresa_id=${empresaId}&tipo_tributo=${tipoTributo}&year=${year}&month=${month}`;

  // Buscar dados do dashboard
  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      // Esconder loading
      hideLoading();

      if (data.success) {
        // Renderizar dashboard
        renderizarDashboardAuditoria(data.dashboard, tipoTributo);
      } else {
        // Mostrar mensagem de erro
        const errorMsg = console.error(
          'Erro retornado pelo servidor:',
          errorMsg,
        );
        showErrorMessage(errorMsg);

        // Se não houver dados, mostrar dashboard vazio
        renderizarDashboardVazio(tipoTributo);
      }
    })
    .catch((error) => {
      // Esconder loading
      hideLoading();

      // Mostrar dashboard vazio
      renderizarDashboardVazio(tipoTributo);
    });
}

/**
 * Renderiza o dashboard de auditoria
 * @param {Object} dashboard - Dados do dashboard
 * @param {string} tipoTributo - Tipo de tributo
 */
function renderizarDashboardAuditoria(dashboard, tipoTributo) {
  const sumario = dashboard.sumario;
  const resultadosInconsistentes = dashboard.resultados_inconsistentes;

  // Obter o container do dashboard
  const dashboardContainer = document.getElementById(
    'auditoria-dashboard-container',
  );
  if (!dashboardContainer) {
    return;
  }

  // Limpar o container
  dashboardContainer.innerHTML = '';

  // Criar sistema de tabs
  const tabsContainer = document.createElement('div');
  tabsContainer.className = 'dashboard-tabs-container';

  // Criar navegação das tabs
  const tabsNav = document.createElement('ul');
  tabsNav.className = 'nav nav-tabs mb-4';
  tabsNav.innerHTML = `
    <li class="nav-item">
      <a class="nav-link active" id="resumo-tab" data-bs-toggle="tab" href="#resumo" role="tab">
        <i class="fas fa-chart-pie"></i> Resumo
      </a>
    </li>
    <li class="nav-item">
      <a class="nav-link" id="detalhamento-tab" data-bs-toggle="tab" href="#detalhamento" role="tab">
        <i class="fas fa-table"></i> Detalhamento
      </a>
    </li>
  `;

  // Criar conteúdo das tabs
  const tabsContent = document.createElement('div');
  tabsContent.className = 'tab-content';

  // Tab Resumo
  const resumoTab = document.createElement('div');
  resumoTab.className = 'tab-pane fade show active';
  resumoTab.id = 'resumo';
  resumoTab.setAttribute('role', 'tabpanel');

  // Tab Detalhamento
  const detalhamentoTab = document.createElement('div');
  detalhamentoTab.className = 'tab-pane fade';
  detalhamentoTab.id = 'detalhamento';
  detalhamentoTab.setAttribute('role', 'tabpanel');

  tabsContent.appendChild(resumoTab);
  tabsContent.appendChild(detalhamentoTab);

  tabsContainer.appendChild(tabsNav);
  tabsContainer.appendChild(tabsContent);
  dashboardContainer.appendChild(tabsContainer);

  // Renderizar conteúdo da tab Resumo
  renderizarTabResumo(resumoTab, sumario, resultadosInconsistentes);

  // Configurar evento para carregar detalhamento quando a tab for ativada
  const detalhamentoTabLink = document.getElementById('detalhamento-tab');
  detalhamentoTabLink.addEventListener('shown.bs.tab', function () {
    carregarDetalhamentoAuditoria(tipoTributo, detalhamentoTab);
  });

  // Adicionar botões de relatório após renderizar o dashboard
  setTimeout(() => {
    adicionarBotoesRelatorio();
  }, 500);
}

/**
 * Renderiza o conteúdo da tab Resumo
 * @param {HTMLElement} container - Container da tab
 * @param {Object} sumario - Dados do sumário
 * @param {Array} resultadosInconsistentes - Resultados inconsistentes
 */
function renderizarTabResumo(container, sumario, resultadosInconsistentes) {
  // Criar os cards principais
  const cardsRow = document.createElement('div');
  cardsRow.className = 'row mb-4';

  // Calcular valores para os cards
  const valorConforme =
    sumario.valor_total_notas -
    sumario.valor_inconsistente_maior -
    sumario.valor_inconsistente_menor;
  const totalInconsistente =
    sumario.valor_inconsistente_maior + sumario.valor_inconsistente_menor;

  // Usar os campos específicos de notas conformes e inconsistentes se disponíveis
  // Caso contrário, usar a lógica de estimativa anterior
  const notasConformes =
    sumario.notas_conformes !== undefined
      ? sumario.notas_conformes
      : sumario.total_notas -
        Math.min(sumario.total_inconsistente, sumario.total_notas);

  const notasInconsistentes =
    sumario.notas_inconsistentes !== undefined
      ? sumario.notas_inconsistentes
      : Math.min(sumario.total_inconsistente, sumario.total_notas);

  // Card TOTAL
  cardsRow.innerHTML += `
    <div class="col-md-4">
      <div class="card dashboard-card">
        <div class="card-body">
          <h5 class="card-title">TOTAL</h5>
          <div class="card-content">
            <div class="card-value">${formatCurrency(
              sumario.valor_total_notas,
            )}</div>
            <div class="card-subtitle">Total de notas: ${
              sumario.total_notas
            }</div>
            <div class="card-subtitle">Total de itens: ${
              sumario.total_produtos
            }</div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Card de Conformidade
  cardsRow.innerHTML += `
    <div class="col-md-4">
      <div class="card dashboard-card">
        <div class="card-body">
          <h5 class="card-title">Conformidade</h5>
          <div class="card-content">
            <div class="card-value text-success">${formatCurrency(
              valorConforme,
            )}</div>
            <div class="card-subtitle">Notas conforme: ${notasConformes}</div>
            <div class="card-subtitle">Itens conforme: ${
              sumario.total_conforme
            }</div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Card de Inconsistência (clicável)
  const inconsistenciaColorClass = totalInconsistente > 0 ? 'text-danger' : '';
  cardsRow.innerHTML += `
    <div class="col-md-4">
      <div class="card dashboard-card card-clickable" onclick="navegarParaDetalhamento()" style="cursor: pointer;">
        <div class="card-body">
          <h5 class="card-title">Inconsistência</h5>
          <div class="card-content">
            <div class="card-value ${inconsistenciaColorClass}">${formatCurrency(
    totalInconsistente,
  )}</div>
            <div class="card-subtitle">Notas inconsistentes: ${notasInconsistentes}</div>
            <div class="card-subtitle">Itens inconsistentes: ${
              sumario.total_inconsistente
            }</div>
          </div>
        </div>
      </div>
    </div>
  `;

  container.appendChild(cardsRow);

  // Criar os cards de valores a maior e a menor
  const valoresRow = document.createElement('div');
  valoresRow.className = 'row mb-4';

  // Card de Valores a Maior
  const valorMaiorColorClass =
    sumario.valor_inconsistente_maior > 0 ? 'text-danger' : '';
  valoresRow.innerHTML += `
    <div class="col-md-6">
      <div class="card dashboard-card">
        <div class="card-body">
          <h5 class="card-title">Valores a Maior</h5>
          <div class="card-content">
            <div class="card-value ${valorMaiorColorClass}">${formatCurrency(
    sumario.valor_inconsistente_maior,
  )}</div>
            <div class="card-subtitle">Valores calculados maiores que os da nota</div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Card de Valores a Menor
  const valorMenorColorClass =
    sumario.valor_inconsistente_menor > 0 ? 'text-danger' : '';
  valoresRow.innerHTML += `
    <div class="col-md-6">
      <div class="card dashboard-card">
        <div class="card-body">
          <h5 class="card-title">Valores a Menor</h5>
          <div class="card-content">
            <div class="card-value ${valorMenorColorClass}">${formatCurrency(
    sumario.valor_inconsistente_menor,
  )}</div>
            <div class="card-subtitle">Valores calculados menores que os da nota</div>
          </div>
        </div>
      </div>
    </div>
  `;

  container.appendChild(valoresRow);

  // Criar resumo de inconsistências
  if (resultadosInconsistentes.length > 0) {
    const totalInconsistentes =
      sumario.total_inconsistente || resultadosInconsistentes.length;
    const inconsistentesVistas = sumario.total_inconsistentes_vistas || 0;
    const inconsistentesNaoVistas =
      sumario.total_inconsistentes_nao_vistas ||
      totalInconsistentes - inconsistentesVistas;

    const resumoInconsistencias = document.createElement('div');
    resumoInconsistencias.className = 'alert alert-warning mt-4';
    resumoInconsistencias.innerHTML = `
      <h5><i class="fas fa-exclamation-triangle"></i> Resumo de Inconsistências</h5>
      <p>Foram encontradas <strong>${totalInconsistentes}</strong> inconsistências neste período.</p>
      <div class="row mt-3">
        <div class="col-md-6">
          <div class="d-flex align-items-center">
            <i class="fas fa-eye text-success me-2"></i>
            <span><strong>${inconsistentesVistas}</strong> inconsistências foram analisadas pelo analista</span>
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex align-items-center">
            <i class="fas fa-eye-slash text-warning me-2"></i>
            <span><strong>${inconsistentesNaoVistas}</strong> inconsistências aguardam análise</span>
          </div>
        </div>
      </div>
      <p class="mt-3">Acesse a aba <strong>Detalhamento</strong> para visualizar e filtrar os dados completos.</p>
    `;
    container.appendChild(resumoInconsistencias);
  }
}

/**
 * Navega para a aba de detalhamento quando o card de Inconsistência é clicado
 */
function navegarParaDetalhamento() {
  // Encontrar e ativar a aba de detalhamento
  const detalhamentoTabLink = document.getElementById('detalhamento-tab');
  if (detalhamentoTabLink) {
    // Usar Bootstrap para ativar a aba
    const tab = new bootstrap.Tab(detalhamentoTabLink);
    tab.show();

    // Scroll suave para a seção de detalhamento após um pequeno delay
    setTimeout(() => {
      const detalhamentoTab = document.getElementById('detalhamento');
      if (detalhamentoTab) {
        detalhamentoTab.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    }, 300);
  }
}

/**
 * Carrega os dados de detalhamento da auditoria
 * OTIMIZAÇÃO: Agora carrega apenas inconsistências por padrão para melhor performance
 * @param {string} tipoTributo - Tipo de tributo
 * @param {HTMLElement} container - Container da tab
 */
function carregarDetalhamentoAuditoria(tipoTributo, container) {
  // Verificar se já foi carregado
  if (container.hasAttribute('data-loaded')) {
    return;
  }

  // Mostrar loading
  container.innerHTML =
    '<div class="text-center p-4"><i class="fas fa-spinner fa-spin"></i> Carregando detalhamento...</div>';

  // Obter empresa, ano e mês selecionados
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  if (!empresaId) {
    container.innerHTML =
      '<div class="alert alert-warning">Selecione uma empresa para visualizar o detalhamento.</div>';
    return;
  }

  // Construir URL com filtros - mostrar apenas inconsistências por padrão
  const url = `http://127.0.0.1:5000/api/auditoria/dashboard/detalhamento?empresa_id=${empresaId}&tipo_tributo=${tipoTributo}&year=${year}&month=${month}&status=inconsistente`;

  // Buscar dados do detalhamento
  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        renderizarDetalhamentoAuditoria(
          container,
          data.resultados,
          tipoTributo,
        );
        container.setAttribute('data-loaded', 'true');
      } else {
        container.innerHTML = `<div class="alert alert-danger">Erro ao carregar detalhamento: ${data.message}</div>`;
      }
    })
    .catch((error) => {
      container.innerHTML = `<div class="alert alert-danger">Erro ao carregar detalhamento: ${error.message}</div>`;
    });
}

/**
 * Renderiza a tabela de detalhamento da auditoria
 * @param {HTMLElement} container - Container da tab
 * @param {Array} resultados - Dados dos resultados
 * @param {string} tipoTributo - Tipo de tributo
 */
function renderizarDetalhamentoAuditoria(container, resultados, tipoTributo) {
  container.innerHTML = '';

  // Criar filtros
  const filtrosContainer = document.createElement('div');
  filtrosContainer.className = 'audit-dashboard-filters';
  filtrosContainer.innerHTML = `
    <div class="row">
      <div class="col-md-2">
        <label class="form-label">Análise</label>
        <select class="form-select" id="filtro-analista-visualizou">
          <option value="">Todas</option>
          <option value="true">Analisadas</option>
          <option value="false">Não analisadas</option>
        </select>
      </div>
      <div class="col-md-2">
        <label class="form-label">Atividade</label>
        <select class="form-select" id="filtro-atividade">
          <option value="">Todas</option>
          <option value="Indústria ou Equiparado">Indústria</option>
          <option value="Comércio Varejista">Comércio Varejista</option>
          <option value="Comércio Atacadista">Comércio Atacadista</option>
          <option value="Distribuidor">Distribuidor</option>
          <option value="Produtor Rural">Produtor Rural</option>
          <option value="Consumidor Final">Consumidor Final</option>
          <option value="Não Contribuinte">Não Contribuinte</option>
          <option value="Órgão Público">Órgão Público</option>
          <option value="Serviços">Serviços</option>
        </select>
      </div>
      <div class="col-md-2">
        <label class="form-label">Destinação</label>
        <select class="form-select" id="filtro-destinacao">
          <option value="">Todas</option>
          <option value="Industrialização">Industrialização</option>
          <option value="Revenda">Revenda</option>
          <option value="Ativo Imobilizado">Ativo Imobilizado</option>
          <option value="Uso e Consumo">Uso e Consumo</option>
        </select>
      </div>
      <div class="col-md-2">
        <label class="form-label">Número NF</label>
        <input type="text" class="form-control" id="filtro-numero" placeholder="Filtrar por número">
      </div>
      <div class="col-md-2">
        <label class="form-label">Produto</label>
        <input type="text" class="form-control" id="filtro-produto" placeholder="Filtrar por produto">
      </div>
    </div>

    <!-- Filtros Dropdown - Estilo Cenários Detalhes -->
    <div class="row mt-3">
      <!-- Filtro de CFOP -->
      <div class="col-md-3 mb-3">
        <label class="form-label">CFOP</label>
        <div class="dropdown w-100">
          <input type="text"
                 class="form-control dropdown-toggle column-filter-dropdown"
                 id="input-cfop-auditoria"
                 data-bs-toggle="dropdown"
                 data-bs-auto-close="outside"
                 data-filter-type="cfop"
                 placeholder="Selecionar CFOPs..."
                 readonly
                 style="cursor: pointer;">
          <div class="dropdown-menu p-2" id="dropdown-cfop-auditoria" style="min-width: 250px; max-height: 300px; overflow-y: auto;">
            <div class="mb-2">
              <input type="text"
                     class="form-control form-control-sm"
                     id="busca-cfop-auditoria"
                     placeholder="Buscar CFOP..."
                     onkeyup="filtrarOpcoesDropdownAuditoria('cfop')">
            </div>
            <div class="mb-2">
              <div class="d-flex justify-content-between">
                <button type="button"
                        class="btn btn-sm btn-outline-primary"
                        onclick="selecionarTodosDropdownAuditoria('cfop')">
                  Todos
                </button>
                <button type="button"
                        class="btn btn-sm btn-outline-secondary"
                        onclick="limparSelecaoDropdownAuditoria('cfop')">
                  Limpar
                </button>
              </div>
            </div>
            <div id="container-cfop-auditoria" class="opcoes-container">
              <!-- Checkboxes serão adicionados dinamicamente -->
            </div>
          </div>
        </div>
      </div>

      <!-- Filtro de NCM -->
      <div class="col-md-3 mb-3">
        <label class="form-label">NCM</label>
        <div class="dropdown w-100">
          <input type="text"
                 class="form-control dropdown-toggle column-filter-dropdown"
                 id="input-ncm-auditoria"
                 data-bs-toggle="dropdown"
                 data-bs-auto-close="outside"
                 data-filter-type="ncm"
                 placeholder="Selecionar NCMs..."
                 readonly
                 style="cursor: pointer;">
          <div class="dropdown-menu p-2" id="dropdown-ncm-auditoria" style="min-width: 250px; max-height: 300px; overflow-y: auto;">
            <div class="mb-2">
              <input type="text"
                     class="form-control form-control-sm"
                     id="busca-ncm-auditoria"
                     placeholder="Buscar NCM..."
                     onkeyup="filtrarOpcoesDropdownAuditoria('ncm')">
            </div>
            <div class="mb-2">
              <div class="d-flex justify-content-between">
                <button type="button"
                        class="btn btn-sm btn-outline-primary"
                        onclick="selecionarTodosDropdownAuditoria('ncm')">
                  Todos
                </button>
                <button type="button"
                        class="btn btn-sm btn-outline-secondary"
                        onclick="limparSelecaoDropdownAuditoria('ncm')">
                  Limpar
                </button>
              </div>
            </div>
            <div id="container-ncm-auditoria" class="opcoes-container">
              <!-- Checkboxes serão adicionados dinamicamente -->
            </div>
          </div>
        </div>
      </div>

      <!-- Filtro de CST -->
      <div class="col-md-3 mb-3">
        <label class="form-label">CST</label>
        <div class="dropdown w-100">
          <input type="text"
                 class="form-control dropdown-toggle column-filter-dropdown"
                 id="input-cst-auditoria"
                 data-bs-toggle="dropdown"
                 data-bs-auto-close="outside"
                 data-filter-type="cst"
                 placeholder="Selecionar CSTs..."
                 readonly
                 style="cursor: pointer;">
          <div class="dropdown-menu p-2" id="dropdown-cst-auditoria" style="min-width: 250px; max-height: 300px; overflow-y: auto;">
            <div class="mb-2">
              <input type="text"
                     class="form-control form-control-sm"
                     id="busca-cst-auditoria"
                     placeholder="Buscar CST..."
                     onkeyup="filtrarOpcoesDropdownAuditoria('cst')">
            </div>
            <div class="mb-2">
              <div class="d-flex justify-content-between">
                <button type="button"
                        class="btn btn-sm btn-outline-primary"
                        onclick="selecionarTodosDropdownAuditoria('cst')">
                  Todos
                </button>
                <button type="button"
                        class="btn btn-sm btn-outline-secondary"
                        onclick="limparSelecaoDropdownAuditoria('cst')">
                  Limpar
                </button>
              </div>
            </div>
            <div id="container-cst-auditoria" class="opcoes-container">
              <!-- Checkboxes serão adicionados dinamicamente -->
            </div>
          </div>
        </div>
      </div>

      <!-- Filtro de Alíquota -->
      <div class="col-md-3 mb-3">
        <label class="form-label">Alíquota</label>
        <div class="dropdown w-100">
          <input type="text"
                 class="form-control dropdown-toggle column-filter-dropdown"
                 id="input-aliquota-auditoria"
                 data-bs-toggle="dropdown"
                 data-bs-auto-close="outside"
                 data-filter-type="aliquota"
                 placeholder="Selecionar Alíquotas..."
                 readonly
                 style="cursor: pointer;">
          <div class="dropdown-menu p-2" id="dropdown-aliquota-auditoria" style="min-width: 250px; max-height: 300px; overflow-y: auto;">
            <div class="mb-2">
              <input type="text"
                     class="form-control form-control-sm"
                     id="busca-aliquota-auditoria"
                     placeholder="Buscar Alíquota..."
                     onkeyup="filtrarOpcoesDropdownAuditoria('aliquota')">
            </div>
            <div class="mb-2">
              <div class="d-flex justify-content-between">
                <button type="button"
                        class="btn btn-sm btn-outline-primary"
                        onclick="selecionarTodosDropdownAuditoria('aliquota')">
                  Todos
                </button>
                <button type="button"
                        class="btn btn-sm btn-outline-secondary"
                        onclick="limparSelecaoDropdownAuditoria('aliquota')">
                  Limpar
                </button>
              </div>
            </div>
            <div id="container-aliquota-auditoria" class="opcoes-container">
              <!-- Checkboxes serão adicionados dinamicamente -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Botões de ação para filtros -->
    <div class="row mt-3">
      <div class="col-12 text-center">
        <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="limparTodosFiltros()">
          <i class="fas fa-eraser"></i> Limpar Filtros
        </button>
        <button type="button" class="btn btn-outline-info btn-sm" onclick="mostrarResumoFiltros()">
          <i class="fas fa-info-circle"></i> Resumo dos Filtros
        </button>
      </div>
    </div>
  `;

  container.appendChild(filtrosContainer);

  // Criar tabela
  const tableContainer = document.createElement('div');
  tableContainer.className = 'table-responsive';

  const table = document.createElement('table');
  table.className = 'table table-striped table-bordered';
  table.id = 'audit-dashboard-detail-table';
  table.innerHTML = `
    <thead>
      <tr>
        <th style="width: 40px;"></th>
        <th>Origem</th>
        <th>Número</th>
        <th>CFOP</th>
        <th>Produto</th>
        <th>Descrição</th>
        <th>NCM</th>
        <th>CEST</th>
        <th>CST</th>
        <th>Base de Cálculo</th>
        <th>Alíquota (%)</th>
        <th>Valor</th>
        <th>Status</th>
        <th>Ações</th>
      </tr>
    </thead>
    <tbody id="audit-dashboard-detail-tbody">
    </tbody>
  `;

  tableContainer.appendChild(table);
  container.appendChild(tableContainer);

  // Preencher tabela
  preencherTabelaDetalhamento(resultados);

  // Configurar filtros
  configurarFiltrosDetalhamento(tipoTributo);
}

/**
 * Preenche a tabela de detalhamento com interface de expansão
 * @param {Array} resultados - Dados dos resultados
 */
function preencherTabelaDetalhamento(resultados) {
  const tbody = document.getElementById('audit-dashboard-detail-tbody');
  tbody.innerHTML = '';

  if (resultados.length === 0) {
    tbody.innerHTML = `
      <tr>
        <td colspan="14" class="text-center">Nenhum resultado encontrado</td>
      </tr>
    `;
    return;
  }

  // Filtrar apenas resultados inconsistentes
  const resultadosInconsistentes = resultados.filter(
    (r) => r.status === 'inconsistente',
  );

  if (resultadosInconsistentes.length === 0) {
    tbody.innerHTML = `
      <tr>
        <td colspan="14" class="text-center">Nenhum resultado inconsistente encontrado</td>
      </tr>
    `;
    // Limpar filtros quando não houver resultados
    ['cfop', 'ncm', 'cst', 'aliquota'].forEach(tipo => {
      const container = document.getElementById(`container-${tipo}-auditoria`);
      if (container) container.innerHTML = '';
    });
    return;
  }
  
  // Extrair valores únicos das notas para os filtros
  const valoresFiltro = {
    cfop: new Set(),
    ncm: new Set(),
    cst: new Set(),
    aliquota: new Set()
  };
  
  resultadosInconsistentes.forEach(item => {
    // Usar apenas os valores da nota, não do cenário
    if (item.cfop) valoresFiltro.cfop.add(item.cfop);
    if (item.ncm) valoresFiltro.ncm.add(item.ncm);
    if (item.cst) valoresFiltro.cst.add(item.cst);
    if (item.aliquota) valoresFiltro.aliquota.add(parseFloat(item.aliquota).toFixed(2));
  });
  
  // Atualizar os filtros com os valores extraídos
  atualizarFiltrosComValoresNotas(valoresFiltro);



  resultadosInconsistentes.forEach((resultado) => {
    const notaId = `nota-${resultado.id}`;
    const cenarioId = `cenario-${resultado.id}`;

    // Linha da nota (sempre visível)
    const trNota = document.createElement('tr');
    trNota.className = 'nota-inconsistente-row';
    trNota.id = notaId;

    const aliquotaNotaFormatada = resultado.aliquota
      ? `${parseFloat(resultado.aliquota).toFixed(2)}%`
      : '-';

    trNota.innerHTML = `
      <td>
        <button class="expand-scenario-btn" onclick="toggleScenario('${cenarioId}', this)" title="Mostrar/Ocultar Cenário">
          <i class="fas fa-chevron-right"></i>
        </button>
      </td>
      <td><span class="badge bg-danger">NFe</span></td>
      <td>${resultado.numero || '-'}</td>
      <td>${resultado.cfop || '-'}</td>
      <td>${resultado.produto_numero || '-'}</td>
      <td>${resultado.produto_descricao || '-'}</td>
      <td>${resultado.ncm || '-'}</td>
      <td>${resultado.cest || '-'}</td>
      <td>${resultado.cst || '-'}</td>
      <td class="${
        resultado.comparacao?.base_calculo_diferente ? 'text-danger' : ''
      } text-end">${
      resultado.base_calculo ? formatCurrency(resultado.base_calculo) : '-'
    }</td>
      <td class="${
        resultado.comparacao?.aliquota_diferente ? 'text-danger' : ''
      } text-end">${aliquotaNotaFormatada}</td>
      <td class="${
        resultado.comparacao?.valor_diferente ? 'text-danger' : ''
      } text-end">${formatCurrency(resultado.valor)}</td>
      <td>${getStatusBadge(resultado.status, resultado.cenario_status)}</td>
      <td>
        <div class="action-buttons">
          <button class="btn btn-view" onclick="visualizarDetalhes('nota', ${
            resultado.id
          })" title="Visualizar detalhes">
            <i class="fas fa-eye"></i>
          </button>
          <button class="btn btn-report" onclick="gerarRelatorio(${
            resultado.id
          }, this)" title="Gerar relatório PDF">
            <i class="fas fa-file-pdf"></i>
          </button>
          ${
            resultado.analista_visualizou
              ? `<button class="btn btn-success btn-sm" onclick="visualizarObservacoes(${
                  resultado.id
                }, '${(resultado.observacoes_analista || '').replace(
                  /'/g,
                  "\\'",
                )}', '${
                  resultado.data_visualizacao
                    ? new Date(resultado.data_visualizacao).toLocaleDateString(
                        'pt-BR',
                      )
                    : 'N/A'
                }')" title="Ver observações da análise">
              <i class="fas fa-check"></i>
            </button>`
              : `<button class="btn btn-warning" onclick="marcarComoVista(${resultado.id})" title="Marcar como analisada">
              <i class="fas fa-eye-slash"></i>
            </button>`
          }
        </div>
      </td>
    `;

    tbody.appendChild(trNota);

    // Linha do cenário (inicialmente oculta)
    const trCenario = document.createElement('tr');
    trCenario.className = 'scenario-row';
    trCenario.id = cenarioId;

    const aliquotaCenarioFormatada = resultado.cenario.aliquota
      ? `${parseFloat(resultado.cenario.aliquota).toFixed(2)}%`
      : '-';

    trCenario.innerHTML = `
      <td></td>
      <td>${getCenarioStatusBadge(resultado.cenario_status)}</td>
      <td>${resultado.cenario.id || '-'}</td>
      <td>${resultado.cfop || '-'}</td>
      <td>${resultado.produto_numero || '-'}</td>
      <td>${resultado.produto_descricao || '-'}</td>
      <td>${resultado.ncm || '-'}</td>
      <td>${resultado.cest || '-'}</td>
      <td>${resultado.cenario.cst || '-'}</td>
      <td class="${
        resultado.comparacao?.base_calculo_diferente ? 'text-success' : ''
      } text-end">${
      resultado.cenario.base_calculo
        ? formatCurrency(resultado.cenario.base_calculo)
        : '-'
    }</td>
      <td class="${
        resultado.comparacao?.aliquota_diferente ? 'text-success' : ''
      } text-end">${aliquotaCenarioFormatada}</td>
      <td class="${
        resultado.comparacao?.valor_diferente ? 'text-success' : ''
      } text-end">${formatCurrency(resultado.cenario.valor)}</td>
      <td>${getCenarioStatusBadge(resultado.cenario_status)}</td>
      <td>
        <div class="action-buttons">
          <button class="btn btn-view" onclick="visualizarDetalhes('cenario', ${
            resultado.cenario.id || resultado.id
          })" title="Visualizar detalhes">
            <i class="fas fa-eye"></i>
          </button>
        </div>
      </td>
    `;

    tbody.appendChild(trCenario);
  });
}

/**
 * Retorna o badge de status para a linha da NFe
 * @param {string} status - Status da auditoria ('conforme' ou 'inconsistente')
 * @param {string} cenarioStatus - Status do cenário ('producao' ou 'inconsistente')
 * @returns {string} HTML do badge
 */
function getStatusBadge(status, cenarioStatus) {
  // Se estamos na tabela de detalhamento, só mostramos inconsistências
  // Então sempre mostrar "Inconsistente" (já que só aparecem registros inconsistentes)
  return '<span class="badge bg-danger">Inconsistente</span>';
}

/**
 * Retorna o badge de status para a linha do Cenário
 * @param {string} cenarioStatus - Status do cenário ('producao' ou 'inconsistente')
 * @returns {string} HTML do badge
 */
function getCenarioStatusBadge(cenarioStatus) {
  if (cenarioStatus === 'inconsistente') {
    return '<span class="badge bg-warning">Cenário Inconsistente</span>';
  } else {
    return '<span class="badge bg-success">Cenário</span>';
  }
}

/**
 * Alterna a visibilidade do cenário
 * @param {string} cenarioId - ID da linha do cenário
 * @param {HTMLElement} button - Botão de expansão
 */
function toggleScenario(cenarioId, button) {
  const cenarioRow = document.getElementById(cenarioId);
  const icon = button.querySelector('i');

  if (cenarioRow.classList.contains('show')) {
    // Ocultar cenário
    cenarioRow.classList.remove('show');
    icon.classList.remove('fa-chevron-down');
    icon.classList.add('fa-chevron-right');
    button.classList.remove('expanded');
  } else {
    // Mostrar cenário
    cenarioRow.classList.add('show');
    icon.classList.remove('fa-chevron-right');
    icon.classList.add('fa-chevron-down');
    button.classList.add('expanded');
  }
}

/**
 * Função de debounce para otimizar filtros
 * @param {Function} func - Função a ser executada
 * @param {number} delay - Delay em milissegundos
 * @param {string} key - Chave única para o timer
 */
function debounce(func, delay, key) {
  // Limpar timer anterior se existir
  if (debounceTimers.has(key)) {
    clearTimeout(debounceTimers.get(key));
  }

  // Criar novo timer
  const timer = setTimeout(func, delay);
  debounceTimers.set(key, timer);
}

/**
 * Configura os filtros da tabela de detalhamento
 * @param {string} tipoTributo - Tipo de tributo
 */
function configurarFiltrosDetalhamento(tipoTributo) {
  // Não carregamos mais os filtros de cenários aqui
  // Eles serão preenchidos após carregar os dados das notas
  
  // Adicionar eventos aos botões de filtro
  const botoesFiltro = document.querySelectorAll('.btn-filtro-auditoria');
  botoesFiltro.forEach((botao) => {
    botao.addEventListener('click', function (e) {
      e.stopPropagation();
      const tipo = this.getAttribute('data-tipo');
      const dropdown = document.getElementById(`dropdown-${tipo}-auditoria`);
      
      // Fechar outros dropdowns abertos
      document.querySelectorAll('.dropdown-menu-auditoria').forEach((menu) => {
        if (menu.id !== `dropdown-${tipo}-auditoria`) {
          menu.classList.remove('show');
        }
      });
      
      // Alternar o dropdown atual
      if (dropdown) {
        dropdown.classList.toggle('show');
      }
    });
  });

  // Fechar dropdowns ao clicar fora
  document.addEventListener('click', function (e) {
    if (!e.target.matches('.btn-filtro-auditoria')) {
      document.querySelectorAll('.dropdown-menu-auditoria').forEach((menu) => {
        menu.classList.remove('show');
      });
    }
  });
}

/**
 * Obtém os valores selecionados dos checkboxes de filtro
 * @param {string} type - Tipo de filtro (cfop, ncm, cst, aliquota)
 * @returns {Array} Array com os valores selecionados
 */
function getValoresFiltroSelecionados(type) {
  // Usar a nova estrutura de auditoria
  return getValoresFiltroSelecionadosAuditoria(type);
}

/**
 * Aplica os filtros na tabela de detalhamento
 * @param {string} tipoTributo - Tipo de tributo
 */
function aplicarFiltrosDetalhamento(tipoTributo) {
  // Obter valores dos filtros
  const filtros = {
    analista_visualizou:
      document.getElementById('filtro-analista-visualizou')?.value || '',
    atividade: document.getElementById('filtro-atividade')?.value || '',
    destinacao: document.getElementById('filtro-destinacao')?.value || '',
    numero: document.getElementById('filtro-numero')?.value || '',
    cfop: getValoresFiltroSelecionados('cfop'),
    produto: document.getElementById('filtro-produto')?.value || '',
    ncm: getValoresFiltroSelecionados('ncm'),
    cst: getValoresFiltroSelecionados('cst'),
    aliquota: getValoresFiltroSelecionados('aliquota'),
  };

  // Obter empresa, ano e mês selecionados
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  // Criar chave de cache
  const cacheKey = `${empresaId}-${tipoTributo}-${year}-${month}-${JSON.stringify(filtros)}`;
  
  // Construir URL para buscar os dados
  let url = `/api/auditoria/dashboard/detalhamento?empresa_id=${empresaId}&tipo_tributo=${tipoTributo}&year=${year}&month=${month}`;
  
  // Adicionar filtros à URL
  Object.entries(filtros).forEach(([key, value]) => {
    if (value && value.length > 0) {
      if (Array.isArray(value)) {
        value.forEach(v => {
          if (v) url += `&${key}=${encodeURIComponent(v)}`;
        });
      } else if (value) {
        url += `&${key}=${encodeURIComponent(value)}`;
      }
    }
  });
  
  // Verificar cache
  if (detalhamentoCache.has(cacheKey)) {
    const resultados = detalhamentoCache.get(cacheKey);
    filtrarERenderizar(resultados);
    return;
  }

  // Buscar dados
  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Armazenar no cache
        detalhamentoCache.set(cacheKey, data.resultados);
        filtrarERenderizar(data.resultados);
      }
    })
    .catch((error) => {
      console.error('Erro ao buscar dados:', error);
    });
    
  // Função para filtrar e renderizar os resultados
  function filtrarERenderizar(resultados) {
    // 1. Filtrar apenas resultados inconsistentes
    let resultadosFiltrados = resultados.filter(r => r.status === 'inconsistente');
    
    // 2. Aplicar filtros selecionados
    ['cfop', 'ncm', 'cst', 'aliquota'].forEach(tipo => {
      const checkboxes = document.querySelectorAll(`#container-${tipo}-auditoria input[type="checkbox"]:checked`);
      if (checkboxes.length > 0) {
        const valoresSelecionados = Array.from(checkboxes).map(cb => 
          tipo === 'aliquota' ? parseFloat(cb.value).toFixed(2) : cb.value
        );
        
        resultadosFiltrados = resultadosFiltrados.filter(item => {
          const valorItem = tipo === 'aliquota' 
            ? parseFloat(item[tipo]).toFixed(2) 
            : item[tipo];
          return valoresSelecionados.includes(valorItem);
        });
      }
    });
    
    // 3. Extrair valores únicos para os filtros
    const valoresFiltro = {
      cfop: new Set(),
      ncm: new Set(),
      cst: new Set(),
      aliquota: new Set()
    };
    
    resultadosFiltrados.forEach(item => {
      if (item.cfop) valoresFiltro.cfop.add(item.cfop);
      if (item.ncm) valoresFiltro.ncm.add(item.ncm);
      if (item.cst) valoresFiltro.cst.add(item.cst);
      if (item.aliquota) valoresFiltro.aliquota.add(parseFloat(item.aliquota).toFixed(2));
    });
    
    // 4. Atualizar filtros com os valores extraídos
    atualizarFiltrosComValoresNotas(valoresFiltro);
    
    // 5. Preencher a tabela com os resultados filtrados
    preencherTabelaDetalhamento(resultadosFiltrados);
  }
}

/**

/**
 * Atualiza os filtros com base nos valores extraídos das notas
 * @param {Object} valores - Objeto com os valores únicos para cada tipo de filtro
 */
function atualizarFiltrosComValoresNotas(valores) {
  // Para cada tipo de filtro (cfop, ncm, cst, aliquota)
  Object.keys(valores).forEach(tipo => {
    const container = document.getElementById(`container-${tipo}-auditoria`);
    if (!container) return;
    
    // Limpar o container
    container.innerHTML = '';
    
    // Se não houver valores para este tipo, adicionar mensagem
    if (valores[tipo].size === 0) {
      container.innerHTML = '<div class="small text-muted p-2">Nenhum valor encontrado</div>';
      return;
    }
    
    // Converter Set para array e ordenar
    let valoresOrdenados = Array.from(valores[tipo]).filter(Boolean);
    if (tipo === 'aliquota') {
      valoresOrdenados.sort((a, b) => parseFloat(a) - parseFloat(b));
    } else {
      valoresOrdenados.sort();
    }
    
    // Adicionar checkboxes para cada valor único
    valoresOrdenados.forEach(valor => {
      if (valor) {
        const checkboxId = `checkbox-${tipo}-auditoria-${valor.toString().replace(/[^a-zA-Z0-9]/g, '-')}`;
        const div = document.createElement('div');
        div.className = 'form-check opcao-checkbox';
        div.dataset.value = valor;
        
        div.innerHTML = `
          <input class="form-check-input"
                 type="checkbox"
                 id="${checkboxId}"
                 value="${valor}"
                 data-type="${tipo}">
          <label class="form-check-label small" for="${checkboxId}">
            ${tipo === 'aliquota' ? `${parseFloat(valor).toFixed(2)}%` : valor}
          </label>
        `;
        
        container.appendChild(div);
      }
    });
    
    // Adicionar botão de aplicar filtros
    const btnAplicar = document.createElement('button');
    btnAplicar.className = 'btn btn-success btn-sm w-100 mt-2 text-white';
    btnAplicar.innerHTML = '<i class="fas fa-check me-1"></i> Aplicar Filtros';
    btnAplicar.addEventListener('click', () => {
      atualizarTextoInputDropdown(tipo);
      const path = window.location.pathname.split('/');
      const tipoTributo = path[path.length - 1];
      if (tipoTributo) {
        aplicarFiltrosDetalhamento(tipoTributo);
      }
      // Fechar o dropdown
      const dropdown = document.getElementById(`dropdown-${tipo}-auditoria`);
      if (dropdown) dropdown.classList.remove('show');
    });
    
    container.appendChild(btnAplicar);
    
    // Atualizar texto do input
    atualizarTextoInputDropdown(tipo);
  });
}

/**
 * Atualiza os filtros com base nos resultados atuais da tabela
 * @param {Array} resultados - Resultados atuais da tabela
 */
function atualizarFiltrosComResultados(resultados) {
  // Armazenar resultados atuais para uso posterior
  window.resultadosAtuais = resultados;
  
  // Recarregar os filtros para refletir apenas os valores presentes
  const path = window.location.pathname.split('/');
  const tipoTributo = path[path.length - 1];
  if (tipoTributo) {
    carregarOpcoesFiltrosAuditoria(tipoTributo);
  }
}

/**
 * Cria checkboxes para filtros com relacionamentos no estilo dropdown
 * @param {string} tipo - Tipo de filtro (cfop, ncm, cst, aliquota)
 * @param {Array} opcoes - Array de opções com relacionamentos
 */
function criarCheckboxFiltroComRelacionamentos(tipo, opcoes) {
  const container = document.getElementById(`container-${tipo}-auditoria`);
  if (!container) return;

  // Limpar container
  container.innerHTML = '';

  // Ordenar opções
  let sortedOpcoes = [...opcoes];
  if (tipo === 'aliquota') {
    sortedOpcoes.sort((a, b) => parseFloat(a.value) - parseFloat(b.value));
  } else {
    sortedOpcoes.sort((a, b) => a.value.localeCompare(b.value));
  }

  // Armazenar opções globalmente para filtros relacionados
  if (!window.auditoriaFilters) {
    window.auditoriaFilters = {};
  }
  window.auditoriaFilters[tipo] = sortedOpcoes;
  
  // Usar apenas os valores das notas, não dos cenários
  if (window.valoresFiltroNotas && window.valoresFiltroNotas[tipo]) {
    const valoresNotas = window.valoresFiltroNotas[tipo];
    sortedOpcoes = sortedOpcoes.filter(opcao => {
      const valor = tipo === 'aliquota' ? parseFloat(opcao.value).toFixed(2) : opcao.value;
      return valoresNotas.has(valor);
    });
  }

  sortedOpcoes.forEach((opcao) => {
    if (!opcao.value) return; // Ignorar valores vazios

    const checkboxId = `checkbox-${tipo}-auditoria-${opcao.value
      .toString()
      .replace(/[^a-zA-Z0-9]/g, '-')}`;
    const div = document.createElement('div');
    div.className = 'form-check opcao-checkbox';
    div.dataset.value = opcao.value;

    div.innerHTML = `
      <input class="form-check-input"
             type="checkbox"
             id="${checkboxId}"
             value="${opcao.value}"
             data-type="${tipo}"
             data-related='${JSON.stringify(opcao.related)}'>
      <label class="form-check-label small" for="${checkboxId}">
        ${
          tipo === 'aliquota'
            ? `${parseFloat(opcao.value).toFixed(2)}%`
            : opcao.value
        }
      </label>
    `;
    container.appendChild(div);
  });

  // Adicionar eventos de mudança nos checkboxes
  const checkboxes = container.querySelectorAll('input[type="checkbox"]');
  checkboxes.forEach((checkbox) => {
    checkbox.addEventListener('change', () => {
      // Atualizar texto do input
      atualizarTextoInputDropdown(tipo);

      // Aplicar filtros relacionados
      aplicarFiltrosRelacionadosAuditoria();

      // Aplicar filtros na tabela
      const path = window.location.pathname;
      const tipoTributo = path.split('/').pop();
      if (tipoTributo && tipoTributo !== 'auditoria') {
        aplicarFiltrosDetalhamento(tipoTributo);
      }
    });
  });
  
  // Atualizar texto do input após carregar os filtros
  atualizarTextoInputDropdown(tipo);


}

/**
 * Atualiza o texto do input do dropdown com base nas opções selecionadas
 * @param {string} tipo - Tipo do filtro (cfop, ncm, cst, aliquota)
 */
function atualizarTextoInputDropdown(tipo) {
  const input = document.getElementById(`input-${tipo}-auditoria`);
  if (!input) {
    console.error(`Input não encontrado: input-${tipo}-auditoria`);
    return;
  }

  // Encontrar todos os checkboxes marcados para este tipo
  const checkboxes = document.querySelectorAll(
    `#container-${tipo}-auditoria input[type="checkbox"]:checked`,
  );

  if (checkboxes.length === 0) {
    input.value = '';
    input.placeholder = `Filtrar por ${tipo.toUpperCase()}`;
    return;
  }

  // Se houver apenas um item selecionado, mostrar o valor
  if (checkboxes.length === 1) {
    input.value = checkboxes[0].value;
    return;
  }

  // Se houver mais de um item, mostrar a quantidade
  input.value = `${checkboxes.length} itens selecionados`;
  
  // Forçar o evento de input para garantir que o valor seja atualizado
  const event = new Event('input', { bubbles: true });
  input.dispatchEvent(event);
}

/**
 * Filtra as opções do dropdown baseado na busca
 * @param {string} tipo - Tipo do filtro
 */
function filtrarOpcoesDropdownAuditoria(tipo) {
  const buscaInput = document.getElementById(`busca-${tipo}-auditoria`);
  const container = document.getElementById(`container-${tipo}-auditoria`);

  if (!buscaInput || !container) return;

  const termoBusca = buscaInput.value.toLowerCase();
  const checkboxes = container.querySelectorAll('.opcao-checkbox');

  checkboxes.forEach((checkbox) => {
    const valor = checkbox.dataset.value.toLowerCase();
    const shouldShow = valor.includes(termoBusca);
    checkbox.style.display = shouldShow ? 'block' : 'none';
  });
}

/**
 * Seleciona todos os itens do dropdown
 * @param {string} tipo - Tipo do filtro
 */
function selecionarTodosDropdownAuditoria(tipo) {
  const container = document.getElementById(`container-${tipo}-auditoria`);
  if (!container) return;

  const checkboxes = container.querySelectorAll(
    'input[type="checkbox"]:not(:checked)',
  );
  checkboxes.forEach((checkbox) => {
    if (checkbox.closest('.opcao-checkbox').style.display !== 'none') {
      checkbox.checked = true;
    }
  });

  atualizarTextoInputDropdown(tipo);
  aplicarFiltrosRelacionadosAuditoria();

  // Aplicar filtros na tabela
  const path = window.location.pathname;
  const tipoTributo = path.split('/').pop();
  if (tipoTributo && tipoTributo !== 'auditoria') {
    aplicarFiltrosDetalhamento(tipoTributo);
  }
}

/**
 * Limpa a seleção do dropdown
 * @param {string} tipo - Tipo do filtro
 */
function limparSelecaoDropdownAuditoria(tipo) {
  const container = document.getElementById(`container-${tipo}-auditoria`);
  if (!container) return;

  const checkboxes = container.querySelectorAll(
    'input[type="checkbox"]:checked',
  );
  checkboxes.forEach((checkbox) => {
    checkbox.checked = false;
  });

  atualizarTextoInputDropdown(tipo);
  aplicarFiltrosRelacionadosAuditoria();

  // Aplicar filtros na tabela
  const path = window.location.pathname;
  const tipoTributo = path.split('/').pop();
  if (tipoTributo && tipoTributo !== 'auditoria') {
    aplicarFiltrosDetalhamento(tipoTributo);
  }
}

/**
 * Aplica filtros relacionados entre CFOP, NCM, CST e Alíquota para auditoria
 */
function aplicarFiltrosRelacionadosAuditoria() {
  const cfopsSelecionados = getValoresFiltroSelecionadosAuditoria('cfop');
  const ncmsSelecionados = getValoresFiltroSelecionadosAuditoria('ncm');
  const cstsSelecionados = getValoresFiltroSelecionadosAuditoria('cst');
  const aliquotasSelecionadas =
    getValoresFiltroSelecionadosAuditoria('aliquota');

  // Se não há filtros selecionados, mostrar todos
  if (
    cfopsSelecionados.length === 0 &&
    ncmsSelecionados.length === 0 &&
    cstsSelecionados.length === 0 &&
    aliquotasSelecionadas.length === 0
  ) {
    mostrarTodosOsFiltrosAuditoria();
    return;
  }

  // Aplicar filtros adicionais se houver
  const filtrosAtivos = {};
  ['cfop', 'ncm', 'cst', 'aliquota'].forEach(tipo => {
    const checkboxes = document.querySelectorAll(`#container-${tipo}-auditoria input[type="checkbox"]:checked`);
    const valores = Array.from(checkboxes).map(cb => cb.value);
    if (valores.length > 0) {
      filtrosAtivos[tipo] = valores;
    }
  });

  // Armazenar filtros ativos para uso posterior
  window.filtrosAtivos = filtrosAtivos;

  // Filtrar NCMs baseado nos CFOPs selecionados
  if (cfopsSelecionados.length > 0) {
    filtrarOpcoesBaseadoEmSelecaoAuditoria('ncm', 'cfops', cfopsSelecionados);

    // Se há CFOPs selecionados mas nenhum NCM, filtrar CSTs e alíquotas baseado nos CFOPs
    if (ncmsSelecionados.length === 0) {
      filtrarOpcoesBaseadoEmSelecaoAuditoria('cst', 'cfops', cfopsSelecionados);
      filtrarOpcoesBaseadoEmSelecaoAuditoria(
        'aliquota',
        'cfops',
        cfopsSelecionados,
      );
    }
  }

  // Filtrar CSTs baseado nos NCMs selecionados
  if (ncmsSelecionados.length > 0) {
    filtrarOpcoesBaseadoEmSelecaoAuditoria('cst', 'ncms', ncmsSelecionados);

    // Se há NCMs selecionados mas nenhum CST, filtrar alíquotas baseado nos NCMs
    if (cstsSelecionados.length === 0) {
      filtrarOpcoesBaseadoEmSelecaoAuditoria(
        'aliquota',
        'ncms',
        ncmsSelecionados,
      );
    }
  }

  // Filtrar Alíquotas baseado nos CSTs selecionados
  if (cstsSelecionados.length > 0) {
    filtrarOpcoesBaseadoEmSelecaoAuditoria(
      'aliquota',
      'csts',
      cstsSelecionados,
    );
  }
}

/**
 * Obtém os valores selecionados dos checkboxes de filtro para auditoria
 * @param {string} type - Tipo de filtro (cfop, ncm, cst, aliquota)
 * @returns {Array} Array com os valores selecionados
 */
function getValoresFiltroSelecionadosAuditoria(type) {
  const checkboxes = document.querySelectorAll(
    `#container-${type}-auditoria input[type="checkbox"]:checked`,
  );
  return Array.from(checkboxes).map((cb) => cb.value);
}

/**
 * Filtra opções baseado em uma seleção de valores para auditoria
 * @param {string} targetType - Tipo de opção a ser filtrada
 * @param {string} relatedKey - Chave do relacionamento
 * @param {Array} selectedValues - Valores selecionados
 */
function filtrarOpcoesBaseadoEmSelecaoAuditoria(
  targetType,
  relatedKey,
  selectedValues,
) {
  const checkboxes = document.querySelectorAll(
    `#container-${targetType}-auditoria input[type="checkbox"]`,
  );

  checkboxes.forEach((checkbox) => {
    try {
      const related = JSON.parse(checkbox.dataset.related || '{}');
      const relatedValues = related[relatedKey] || [];

      // Verificar se algum dos valores selecionados está nos valores relacionados
      const hasMatch = selectedValues.some((value) =>
        relatedValues.includes(value),
      );

      const formCheck = checkbox.closest('.opcao-checkbox');
      if (hasMatch) {
        formCheck.style.display = 'block';
      } else {
        formCheck.style.display = 'none';
        // Se estiver ocultando, desmarcar o checkbox
        if (checkbox.checked) {
          checkbox.checked = false;
          // Atualizar texto do input
          atualizarTextoInputDropdown(targetType);
        }
      }
    } catch (e) {}
  });
}

/**
 * Mostra todos os filtros (remove filtros relacionados) para auditoria
 */
function mostrarTodosOsFiltrosAuditoria() {
  ['cfop', 'ncm', 'cst', 'aliquota'].forEach((tipo) => {
    const checkboxes = document.querySelectorAll(
      `#container-${tipo}-auditoria input[type="checkbox"]`,
    );
    checkboxes.forEach((checkbox) => {
      checkbox.closest('.opcao-checkbox').style.display = 'block';
    });
  });
}

/**
 * Mostra todos os filtros (remove filtros relacionados)
 */
function mostrarTodosOsFiltros() {
  ['cfop', 'ncm', 'cst', 'aliquota'].forEach((tipo) => {
    const checkboxes = document.querySelectorAll(
      `.filtro-checkbox[data-type="${tipo}"]`,
    );
    checkboxes.forEach((checkbox) => {
      checkbox.closest('.form-check').style.display = 'block';
    });
  });
}

/**
 * Limpa todos os filtros selecionados
 */
function limparTodosFiltros() {
  ['cfop', 'ncm', 'cst', 'aliquota'].forEach((tipo) => {
    limparSelecaoDropdownAuditoria(tipo);
  });
}

/**
 * Mostra resumo dos filtros ativos
 */
function mostrarResumoFiltros() {
  const resumo = {
    cfop: getValoresFiltroSelecionadosAuditoria('cfop'),
    ncm: getValoresFiltroSelecionadosAuditoria('ncm'),
    cst: getValoresFiltroSelecionadosAuditoria('cst'),
    aliquota: getValoresFiltroSelecionadosAuditoria('aliquota'),
  };

  let mensagem = 'Filtros Ativos:\n\n';
  let temFiltros = false;

  Object.keys(resumo).forEach((tipo) => {
    if (resumo[tipo].length > 0) {
      mensagem += `${tipo.toUpperCase()}: ${resumo[tipo].join(', ')}\n`;
      temFiltros = true;
    }
  });

  if (!temFiltros) {
    mensagem += 'Nenhum filtro ativo.';
  }

  alert(mensagem);
}

/**
 * Filtra opções relacionadas com base em um filtro selecionado
 * @param {string} targetType - Tipo de opção a ser filtrada (ex: 'ncm')
 * @param {string} filterType - Tipo do filtro selecionado (ex: 'cfop')
 * @param {string} filterValue - Valor do filtro selecionado
 */
function filtrarOpcoesRelacionadas(targetType, filterType, filterValue) {
  const checkboxes = document.querySelectorAll(
    `.filtro-checkbox[data-type="${targetType}"]`,
  );

  checkboxes.forEach((checkbox) => {
    const relatedValue = checkbox.dataset[filterType];
    // Se o checkbox tem um valor relacionado ao filtro, mostrar/ocultar com base na correspondência
    if (relatedValue) {
      const shouldShow = relatedValue === filterValue;
      checkbox.closest('.form-check').style.display = shouldShow
        ? 'block'
        : 'none';
    }
  });
}

/**
 * Cria os checkboxes para um tipo de filtro
 * @param {string} tipo - Tipo de filtro (cfop, ncm, cst, aliquota)
 * @param {Array} valores - Valores para os checkboxes
 */
function criarCheckboxFiltro(tipo, valores) {
  const container = document.getElementById(`${tipo}-container`);
  if (!container) return;

  // Ordenar valores (exceto para alíquotas que devem manter a ordem numérica)
  if (tipo !== 'aliquota') {
    valores = [...new Set(valores)].sort();
  } else {
    // Para alíquotas, converter para número, ordenar e depois voltar para string
    valores = [...new Set(valores)]
      .map((v) => parseFloat(v))
      .sort((a, b) => a - b)
      .map((v) => v.toString());
  }

  // Limpar container
  container.innerHTML = '';

  // Adicionar campo de busca
  const searchId = `busca-${tipo}`;
  const searchDiv = document.createElement('div');
  searchDiv.className = 'mb-2';
  searchDiv.innerHTML = `
    <input type="text"
           id="${searchId}"
           class="form-control form-control-sm"
           placeholder="Buscar ${tipo.toUpperCase()}...">
  `;
  container.appendChild(searchDiv);

  // Adicionar checkboxes
  const optionsDiv = document.createElement('div');
  optionsDiv.className = 'filter-options-list';
  optionsDiv.style.maxHeight = '200px';
  optionsDiv.style.overflowY = 'auto';

  valores.forEach((valor) => {
    if (!valor) return; // Ignorar valores vazios

    const checkboxId = `filtro-${tipo}-${valor
      .toString()
      .replace(/[^a-zA-Z0-9]/g, '-')}`;
    const div = document.createElement('div');
    div.className = 'form-check';
    div.innerHTML = `
      <input class="form-check-input filtro-checkbox"
             type="checkbox"
             id="${checkboxId}"
             value="${valor}"
             data-type="${tipo}">
      <label class="form-check-label" for="${checkboxId}">
        ${tipo === 'aliquota' ? `${parseFloat(valor).toFixed(2)}%` : valor}
      </label>
    `;
    optionsDiv.appendChild(div);
  });

  container.appendChild(optionsDiv);

  // Adicionar evento de busca
  const searchInput = document.getElementById(searchId);
  if (searchInput) {
    searchInput.addEventListener('input', (e) => {
      const searchTerm = e.target.value.toLowerCase();
      const checkboxes = optionsDiv.querySelectorAll('.form-check');

      checkboxes.forEach((checkbox) => {
        const label = checkbox.querySelector('.form-check-label');
        if (label && label.textContent.toLowerCase().includes(searchTerm)) {
          checkbox.style.display = 'block';
        } else {
          checkbox.style.display = 'none';
        }
      });
    });
  }

  // Adicionar evento de mudança nos checkboxes
  const checkboxes = optionsDiv.querySelectorAll('.filtro-checkbox');
  checkboxes.forEach((checkbox) => {
    checkbox.addEventListener('change', () => {
      // Extrair tipo de tributo da URL
      const path = window.location.pathname;
      const tipoTributo = path.split('/').pop();

      // Se for um CFOP, filtrar NCMs relacionados
      if (tipo === 'cfop' && checkbox.checked) {
        // Desmarcar outros CFOPs (seletor único)
        checkboxes.forEach((cb) => {
          if (cb !== checkbox) cb.checked = false;
        });

        // Filtrar NCMs relacionados
        filtrarOpcoesRelacionadas('ncm', 'cfop', checkbox.value);

        // Limpar seleção de NCMs
        document
          .querySelectorAll('.filtro-checkbox[data-type="ncm"]')
          .forEach((cb) => {
            cb.checked = false;
          });
      }

      // Se for um NCM, verificar se há um CFOP selecionado
      if (tipo === 'ncm' && checkbox.checked) {
        const cfopSelecionado = document.querySelector(
          '.filtro-checkbox[data-type="cfop"]:checked',
        );
        if (!cfopSelecionado) {
          checkbox.checked = false;
          alert('Selecione um CFOP antes de selecionar um NCM.');
          return;
        }
      }

      if (tipoTributo && tipoTributo !== 'auditoria') {
        aplicarFiltrosDetalhamento(tipoTributo);
      }
    });
  });
}

/**
 * Carrega as opções para autocomplete dos filtros
 * @param {string} tipoTributo - Tipo de tributo
 */
function carregarOpcoesAutocomplete(tipoTributo) {
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  // Criar chave de cache para opções
  const cacheKey = `opcoes-${empresaId}-${tipoTributo}-${year}-${month}`;

  // Verificar se existe no cache
  if (filtroOptionsCache.has(cacheKey)) {
    preencherOpcoesAutocomplete(filtroOptionsCache.get(cacheKey));
    return;
  }

  // Buscar opções da API - apenas inconsistências
  const url = `http://127.0.0.1:5000/api/auditoria/dashboard/detalhamento?empresa_id=${empresaId}&tipo_tributo=${tipoTributo}&year=${year}&month=${month}&status=inconsistente`;

  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Extrair opções únicas dos resultados
        const opcoes = extrairOpcoesUnicas(data.resultados);

        // Armazenar no cache
        filtroOptionsCache.set(cacheKey, opcoes);

        // Preencher autocomplete
        preencherOpcoesAutocomplete(opcoes);
      }
    })
    .catch((error) => {});
}

/**
 * Extrai opções únicas dos resultados para autocomplete
 * @param {Array} resultados - Array de resultados de auditoria
 */
function extrairOpcoesUnicas(resultados) {
  const cfops = new Set();
  const ncms = new Set();
  const csts = new Set();
  const aliquotas = new Set();

  resultados.forEach((resultado) => {
    if (resultado.cfop)
      cfops.add(JSON.stringify({ value: resultado.cfop, ncm: resultado.ncm }));
    if (resultado.ncm)
      ncms.add(JSON.stringify({ value: resultado.ncm, cfop: resultado.cfop }));
    if (resultado.cst) csts.add(resultado.cst);
    if (resultado.aliquota) aliquotas.add(resultado.aliquota);
  });

  return {
    cfops: Array.from(cfops).map(JSON.parse),
    ncms: Array.from(ncms).map(JSON.parse),
    csts: Array.from(csts).sort(),
    aliquotas: Array.from(aliquotas).sort((a, b) => a - b),
  };
}

/**
 * Cria um elemento de checkbox para filtro
 * @param {string} type - Tipo do filtro (cfop, ncm, cst, aliquota)
 * @param {string} value - Valor do filtro
 * @param {string} label - Texto a ser exibido
 * @param {string} parentId - ID do container pai
 * @param {Object} [relatedValues] - Valores relacionados (ex: {ncm: '1234'} para CFOP)
 */
function criarCheckboxFiltro(type, value, label, parentId, relatedValues = {}) {
  const container = document.createElement('div');
  container.className = 'form-check';

  const id = `filtro-${type}-${value.replace(/[^a-zA-Z0-9]/g, '-')}`;

  const input = document.createElement('input');
  input.className = 'form-check-input filtro-checkbox';
  input.type = 'checkbox';
  input.id = id;
  input.value = value;
  input.dataset.type = type;

  // Adiciona dados relacionados como atributos data
  Object.entries(relatedValues).forEach(([key, val]) => {
    if (val) input.dataset[key] = val;
  });

  const labelEl = document.createElement('label');
  labelEl.className = 'form-check-label';
  labelEl.htmlFor = id;
  labelEl.textContent = label || value;

  container.appendChild(input);
  container.appendChild(labelEl);

  // Adiciona evento para atualizar filtros quando o checkbox for alterado
  input.addEventListener('change', () => {
    const tipoTributo =
      document.querySelector('.nav-pills .active')?.dataset.tributo;
    if (tipoTributo) aplicarFiltrosDetalhamento(tipoTributo);

    // Se for um CFOP, filtrar NCMs relacionados
    if (type === 'cfop' && input.checked) {
      filtrarOpcoesRelacionadas('ncm', 'cfop', value);
    }
  });

  document.getElementById(parentId).appendChild(container);
  return input;
}

/**
 * Filtra opções relacionadas com base em um filtro selecionado
 * @param {string} targetType - Tipo de opção a ser filtrada (ex: 'ncm')
 * @param {string} filterType - Tipo do filtro selecionado (ex: 'cfop')
 * @param {string} filterValue - Valor do filtro selecionado
 */

checkboxes.forEach((checkbox) => {
  const relatedValue = checkbox.dataset[filterType];
  // Se o checkbox tem um valor relacionado ao filtro, mostrar/ocultar com base na correspondência
  if (relatedValue) {
    const shouldShow = relatedValue === filterValue;
    checkbox.closest('.form-check').style.display = shouldShow
      ? 'block'
      : 'none';
  }
});

/**
 * Preenche as opções de filtro como checkboxes
 * @param {Object} opcoes - Objeto com arrays de opções
 */
function preencherOpcoesAutocomplete(opcoes) {
  // Limpar containers existentes
  [
    'cfop-container',
    'ncm-container',
    'cst-container',
    'aliquota-container',
  ].forEach((id) => {
    const container = document.getElementById(id);
    if (container) container.innerHTML = '';
  });

  // Preencher CFOPs
  const uniqueCfops = [];
  opcoes.cfops.forEach((cfop) => {
    if (!uniqueCfops.includes(cfop.value)) {
      uniqueCfops.push(cfop.value);
      criarCheckboxFiltro(
        'cfop',
        cfop.value,
        `CFOP ${cfop.value}`,
        'cfop-container',
        { ncm: cfop.ncm },
      );
    }
  });

  // Preencher NCMs
  const uniqueNcms = [];
  opcoes.ncms.forEach((ncm) => {
    if (!uniqueNcms.includes(ncm.value)) {
      uniqueNcms.push(ncm.value);
      criarCheckboxFiltro(
        'ncm',
        ncm.value,
        `NCM ${ncm.value}`,
        'ncm-container',
        { cfop: ncm.cfop },
      );
    }
  });

  // Preencher CSTs
  opcoes.csts.forEach((cst) => {
    criarCheckboxFiltro('cst', cst, `CST ${cst}`, 'cst-container');
  });

  // Preencher Alíquotas
  opcoes.aliquotas.forEach((aliquota) => {
    criarCheckboxFiltro(
      'aliquota',
      aliquota,
      `${aliquota}%`,
      'aliquota-container',
    );
  });
}

/**
 * Visualiza detalhes de uma nota ou cenário
 * @param {string} tipo - 'nota' ou 'cenario'
 * @param {number} id - ID do registro
 */
function visualizarDetalhes(tipo, id) {
  // Buscar dados detalhados
  const empresaId = localStorage.getItem('selectedCompany');
  const url = `http://127.0.0.1:5000/api/auditoria/detalhes/${tipo}/${id}?empresa_id=${empresaId}`;

  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        mostrarModalDetalhes(tipo, data.detalhes);
      } else {
        alert(`Erro ao carregar detalhes: ${data.message}`);
      }
    })
    .catch((error) => {
      alert(`Erro ao carregar detalhes: ${error.message}`);
    });
}

/**
 * Mostra modal com detalhes da nota ou cenário
 * @param {string} tipo - 'nota' ou 'cenario'
 * @param {Object} detalhes - Dados detalhados
 */
function mostrarModalDetalhes(tipo, detalhes) {
  const modalId = 'modal-detalhes-auditoria';

  // Remover modal existente se houver
  const existingModal = document.getElementById(modalId);
  if (existingModal) {
    existingModal.remove();
  }

  // Criar modal
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = modalId;
  modal.setAttribute('tabindex', '-1');
  modal.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas ${
              tipo === 'nota' ? 'fa-file-invoice' : 'fa-cogs'
            }"></i>
            Detalhes ${tipo === 'nota' ? 'da Nota Fiscal' : 'do Cenário'}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          ${gerarConteudoModal(tipo, detalhes)}
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Mostrar modal
  const bsModal = new bootstrap.Modal(modal);
  bsModal.show();

  // Remover modal do DOM quando fechado
  modal.addEventListener('hidden.bs.modal', () => {
    modal.remove();
  });
}

/**
 * Gera conteúdo do modal baseado no tipo
 * @param {string} tipo - 'nota' ou 'cenario'
 * @param {Object} detalhes - Dados detalhados
 */
function gerarConteudoModal(tipo, detalhes) {
  if (tipo === 'nota') {
    return `
      <div class="row">
        <div class="col-md-6">
          <h6><i class="fas fa-building"></i> Dados do Cliente</h6>
          <form id="cliente-edit-form-auditoria">
            <div class="row mb-3">
              <div class="col-12">
                <label class="form-label"><strong>Razão Social:</strong></label>
                <input type="text" class="form-control" value="${
                  detalhes.cliente?.razao_social || ''
                }" readonly>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <label class="form-label"><strong>CNPJ:</strong></label>
                <input type="text" class="form-control" value="${
                  detalhes.cliente?.cnpj || ''
                }" readonly>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <label class="form-label"><strong>Inscrição Estadual:</strong></label>
                <input type="text" class="form-control" value="${
                  detalhes.cliente?.inscricao_estadual || ''
                }" readonly>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-6">
                <label class="form-label"><strong>Município:</strong></label>
                <input type="text" class="form-control" value="${
                  detalhes.cliente?.municipio || ''
                }" readonly>
              </div>
              <div class="col-6">
                <label class="form-label"><strong>UF:</strong></label>
                <input type="text" class="form-control" value="${
                  detalhes.cliente?.uf || ''
                }" readonly>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-6">
                <label for="cliente-atividade-auditoria" class="form-label"><strong>Atividade:</strong></label>
                <select class="form-select" id="cliente-atividade-auditoria" name="atividade">
                  <option value="">Selecione...</option>
                  <option value="Indústria ou Equiparado" ${
                    detalhes.cliente?.atividade === 'Indústria ou Equiparado' ||
                    detalhes.cliente?.atividade === 'Indústria'
                      ? 'selected'
                      : ''
                  }>Indústria ou Equiparado</option>
                  <option value="Serviço" ${
                    detalhes.cliente?.atividade === 'Serviço' ? 'selected' : ''
                  }>Serviço</option>
                  <option value="Não Contribuinte" ${
                    detalhes.cliente?.atividade === 'Não Contribuinte'
                      ? 'selected'
                      : ''
                  }>Não Contribuinte</option>
                  <option value="Comércio Varejista" ${
                    detalhes.cliente?.atividade === 'Comércio Varejista'
                      ? 'selected'
                      : ''
                  }>Comércio Varejista</option>
                  <option value="Comércio Atacadista" ${
                    detalhes.cliente?.atividade === 'Comércio Atacadista'
                      ? 'selected'
                      : ''
                  }>Comércio Atacadista</option>
                  <option value="Distribuidor" ${
                    detalhes.cliente?.atividade === 'Distribuidor'
                      ? 'selected'
                      : ''
                  }>Distribuidor</option>
                  <option value="Produtor Rural" ${
                    detalhes.cliente?.atividade === 'Produtor Rural'
                      ? 'selected'
                      : ''
                  }>Produtor Rural</option>
                  <option value="Consumidor Final" ${
                    detalhes.cliente?.atividade === 'Consumidor Final'
                      ? 'selected'
                      : ''
                  }>Consumidor Final</option>
                  <option value="Órgão Público" ${
                    detalhes.cliente?.atividade === 'Órgão Público'
                      ? 'selected'
                      : ''
                  }>Órgão Público</option>
                </select>
              </div>
              <div class="col-6">
                <label for="cliente-destinacao-auditoria" class="form-label"><strong>Destinação:</strong></label>
                <select class="form-select" id="cliente-destinacao-auditoria" name="destinacao">
                  <option value="">Selecione...</option>
                  <option value="Revenda" ${
                    detalhes.cliente?.destinacao === 'Revenda' ? 'selected' : ''
                  }>Revenda</option>
                  <option value="Industrialização" ${
                    detalhes.cliente?.destinacao === 'Industrialização'
                      ? 'selected'
                      : ''
                  }>Industrialização</option>
                  <option value="Uso e Consumo" ${
                    detalhes.cliente?.destinacao === 'Uso e Consumo'
                      ? 'selected'
                      : ''
                  }>Uso e Consumo</option>
                  <option value="Ativo Imobilizado" ${
                    detalhes.cliente?.destinacao === 'Ativo Imobilizado'
                      ? 'selected'
                      : ''
                  }>Ativo Imobilizado</option>
                </select>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <button type="button" class="btn btn-primary btn-sm" onclick="salvarClienteAuditoria(${
                  detalhes.cliente?.id
                })">
                  <i class="fas fa-save"></i> Salvar Cliente
                </button>
              </div>
            </div>
          </form>
        </div>
        <div class="col-md-6">
          <h6><i class="fas fa-box"></i> Dados do Produto</h6>
          <table class="table table-sm">
            <tr><td><strong>Código:</strong></td><td>${
              detalhes.produto?.codigo || '-'
            }</td></tr>
            <tr><td><strong>Descrição:</strong></td><td>${
              detalhes.produto?.descricao || '-'
            }</td></tr>
            <tr><td><strong>NCM:</strong></td><td>${
              detalhes.nota_fiscal_item?.ncm || '-'
            }</td></tr>
            <tr><td><strong>CEST:</strong></td><td>${
              detalhes.produto?.cest || '-'
            }</td></tr>
            <tr><td><strong>CFOP:</strong></td><td>${
              detalhes.nota_fiscal_item?.cfop || '-'
            }</td></tr>
          </table>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-12">
          <h6><i class="fas fa-calculator"></i> Dados do Tributo na Nota</h6>
          <table class="table table-sm">
            <tr><td><strong>CST:</strong></td><td>${
              detalhes.tributo?.cst || '-'
            }</td></tr>
            <tr><td><strong>Base de Cálculo:</strong></td><td>${
              detalhes.tributo?.base_calculo
                ? formatCurrency(detalhes.tributo.base_calculo)
                : '-'
            }</td></tr>
            <tr><td><strong>Alíquota:</strong></td><td>${
              detalhes.tributo?.aliquota
                ? parseFloat(detalhes.tributo.aliquota).toFixed(2) + '%'
                : '-'
            }</td></tr>
            <tr><td><strong>Valor:</strong></td><td>${
              detalhes.tributo?.valor
                ? formatCurrency(detalhes.tributo.valor)
                : '-'
            }</td></tr>
            <tr><td><strong>Data Emissão:</strong></td><td>${
              detalhes.tributo?.data_emissao
                ? new Date(detalhes.tributo.data_emissao).toLocaleDateString(
                    'pt-BR',
                  )
                : '-'
            }</td></tr>
          </table>
        </div>
      </div>
    `;
  } else {
    return `
      <div class="row">
        <div class="col-md-6">
          <h6><i class="fas fa-building"></i> Dados do Cliente</h6>
          <table class="table table-sm">
            <tr><td><strong>Razão Social:</strong></td><td>${
              detalhes.cliente?.razao_social || '-'
            }</td></tr>
            <tr><td><strong>CNPJ:</strong></td><td>${
              detalhes.cliente?.cnpj || '-'
            }</td></tr>
            <tr><td><strong>Inscrição Estadual:</strong></td><td>${
              detalhes.cliente?.inscricao_estadual || '-'
            }</td></tr>
            <tr><td><strong>Município:</strong></td><td>${
              detalhes.cliente?.municipio || '-'
            }</td></tr>
            <tr><td><strong>UF:</strong></td><td>${
              detalhes.cliente?.uf || '-'
            }</td></tr>
            <tr><td><strong>Atividade:</strong></td><td>${
              detalhes.cliente?.atividade || '-'
            }</td></tr>
            <tr><td><strong>Destinação:</strong></td><td>${
              detalhes.cliente?.destinacao || '-'
            }</td></tr>
          </table>
        </div>
        <div class="col-md-6">
          <h6><i class="fas fa-box"></i> Dados do Produto</h6>
          <table class="table table-sm">
            <tr><td><strong>Código:</strong></td><td>${
              detalhes.produto?.codigo || '-'
            }</td></tr>
            <tr><td><strong>Descrição:</strong></td><td>${
              detalhes.produto?.descricao || '-'
            }</td></tr>
            <tr><td><strong>NCM:</strong></td><td>${
              detalhes.cenario?.ncm || '-'
            }</td></tr>
            <tr><td><strong>CEST:</strong></td><td>${
              detalhes.produto?.cest || '-'
            }</td></tr>
            <tr><td><strong>CFOP:</strong></td><td>${
              detalhes.cenario?.cfop || '-'
            }</td></tr>
          </table>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-12">
          <h6><i class="fas fa-cogs"></i> Dados do Cenário</h6>
          <table class="table table-sm">
            <tr><td><strong>ID:</strong></td><td>${
              detalhes.cenario?.id || '-'
            }</td></tr>
            <tr><td><strong>Status:</strong></td><td>${
              detalhes.cenario?.status || '-'
            }</td></tr>
            <tr><td><strong>CST:</strong></td><td>${
              detalhes.cenario?.cst || '-'
            }</td></tr>
            <tr><td><strong>Alíquota:</strong></td><td>${
              detalhes.cenario?.aliquota
                ? parseFloat(detalhes.cenario.aliquota).toFixed(2) + '%'
                : '-'
            }</td></tr>
            <tr><td><strong>Validade Início:</strong></td><td>${
              detalhes.cenario?.validade_inicio
                ? new Date(detalhes.cenario.validade_inicio).toLocaleDateString(
                    'pt-BR',
                  )
                : '-'
            }</td></tr>
            <tr><td><strong>Validade Fim:</strong></td><td>${
              detalhes.cenario?.validade_fim
                ? new Date(detalhes.cenario.validade_fim).toLocaleDateString(
                    'pt-BR',
                  )
                : '-'
            }</td></tr>
          </table>
        </div>
      </div>
    `;
  }
}

/**
 * Gera relatório PDF para uma inconsistência
 * @param {number} id - ID do resultado de auditoria
 * @param {HTMLElement} btnElement - Elemento do botão clicado
 */
function gerarRelatorio(id, btnElement = null) {
  const empresaId = localStorage.getItem('selectedCompany');

  // Mostrar loading
  let btn = btnElement;
  if (!btn) {
    // Fallback para encontrar o botão se não foi passado
    btn = document.querySelector(`button[onclick*="gerarRelatorio(${id})"]`);
  }

  let originalContent = '';
  if (btn) {
    originalContent = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
  }

  const url = `http://127.0.0.1:5000/api/auditoria/relatorio/${id}?empresa_id=${empresaId}`;

  fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => {
      if (response.ok) {
        return response.blob();
      }
      throw new Error('Erro ao gerar relatório');
    })
    .then((blob) => {
      // Criar link para download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `relatorio_inconsistencia_${id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    })
    .catch((error) => {
      alert(`Erro ao gerar relatório: ${error.message}`);
    })
    .finally(() => {
      // Restaurar botão
      if (btn) {
        btn.innerHTML = originalContent;
        btn.disabled = false;
      }
    });
}

/**
 * Gera relatório específico por tipo de tributo
 * @param {string} tipoTributo - Tipo do tributo (icms, icms_st, etc.)
 */
function gerarRelatorioTributo(tipoTributo) {
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear');
  const month = localStorage.getItem('selectedMonth');

  if (!empresaId) {
    alert('Selecione uma empresa primeiro');
    return;
  }

  // Construir URL com parâmetros
  const params = new URLSearchParams({
    empresa_id: empresaId,
    status: 'inconsistente', // Por padrão, mostrar apenas inconsistências
  });

  if (year && month) {
    params.append('year', year);
    params.append('month', month);
  }

  const url = `/api/relatorios/tributo/${tipoTributo}?${params.toString()}`;

  // Fazer download com autenticação
  downloadRelatorioComAuth(url, `relatorio_${tipoTributo}_${empresaId}.pdf`);
}

/**
 * Gera relatório geral com todos os tributos
 */
function gerarRelatorioGeral() {
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear');
  const month = localStorage.getItem('selectedMonth');

  if (!empresaId) {
    alert('Selecione uma empresa primeiro');
    return;
  }

  // Construir URL com parâmetros
  const params = new URLSearchParams({
    empresa_id: empresaId,
    status: 'inconsistente', // Por padrão, mostrar apenas inconsistências
  });

  if (year && month) {
    params.append('year', year);
    params.append('month', month);
  }

  const url = `/api/relatorios/geral?${params.toString()}`;

  // Fazer download com autenticação
  downloadRelatorioComAuth(url, `relatorio_geral_${empresaId}.pdf`);
}

/**
 * Faz download de relatório com autenticação
 * @param {string} url - URL do relatório
 * @param {string} filename - Nome do arquivo
 */
function downloadRelatorioComAuth(url, filename) {
  const token = localStorage.getItem('token');

  if (!token) {
    alert('Token de autenticação não encontrado. Faça login novamente.');
    return;
  }

  // Fazer requisição com fetch para incluir headers de autenticação
  fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`Erro ${response.status}: ${response.statusText}`);
      }
      return response.blob();
    })
    .then((blob) => {
      // Criar URL temporária para o blob
      const blobUrl = window.URL.createObjectURL(blob);

      // Criar link temporário para download
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = filename;
      link.style.display = 'none';

      // Adicionar ao DOM, clicar e remover
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Limpar URL temporária
      window.URL.revokeObjectURL(blobUrl);
    })
    .catch((error) => {
      alert(`Erro ao baixar relatório: ${error.message}`);
    });
}

/**
 * Adiciona botões de relatório ao dashboard
 */
function adicionarBotoesRelatorio() {
  // Verificar se já existem os botões
  if (document.getElementById('relatorio-buttons')) {
    return;
  }

  // Encontrar local para inserir os botões (após o conteúdo do dashboard)
  const dashboardContainer =
    document.getElementById('auditoria-dashboard-container') ||
    document.querySelector('.tab-content') ||
    document.querySelector('.dashboard-cards') ||
    document.querySelector('.container-fluid');

  if (!dashboardContainer) {
    return;
  }

  // Criar seção de relatórios
  const relatorioSection = document.createElement('div');
  relatorioSection.id = 'relatorio-buttons';
  relatorioSection.className = 'row mt-4';
  relatorioSection.innerHTML = `
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fas fa-file-pdf"></i>
            Relatórios de Auditoria
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>Relatórios por Tributo</h6>
              <div class="btn-group-vertical w-100" role="group">
                <button type="button" class="btn btn-outline-primary mb-2" onclick="gerarRelatorioTributo('icms')">
                  <i class="fas fa-file-pdf"></i> Relatório ICMS
                </button>
                <button type="button" class="btn btn-outline-primary mb-2" onclick="gerarRelatorioTributo('icms_st')">
                  <i class="fas fa-file-pdf"></i> Relatório ICMS-ST
                </button>
                <button type="button" class="btn btn-outline-primary mb-2" onclick="gerarRelatorioTributo('ipi')">
                  <i class="fas fa-file-pdf"></i> Relatório IPI
                </button>
                <button type="button" class="btn btn-outline-primary mb-2" onclick="gerarRelatorioTributo('pis')">
                  <i class="fas fa-file-pdf"></i> Relatório PIS
                </button>
                <button type="button" class="btn btn-outline-primary mb-2" onclick="gerarRelatorioTributo('cofins')">
                  <i class="fas fa-file-pdf"></i> Relatório COFINS
                </button>
                <button type="button" class="btn btn-outline-primary mb-2" onclick="gerarRelatorioTributo('difal')">
                  <i class="fas fa-file-pdf"></i> Relatório DIFAL
                </button>
              </div>
            </div>
            <div class="col-md-6">
              <h6>Relatório Geral</h6>
              <button type="button" class="btn btn-primary btn-lg w-100" onclick="gerarRelatorioGeral()">
                <i class="fas fa-file-pdf"></i> Relatório Geral (Todos os Tributos)
              </button>
              <p class="text-muted mt-2">
                <small>
                  O relatório geral inclui todos os tipos de tributos em um único documento,
                  com resumo por tributo e detalhamento completo.
                </small>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Inserir após o último elemento do container
  dashboardContainer.appendChild(relatorioSection);
}

/**
 * Renderiza um dashboard vazio
 * @param {string} tipoTributo - Tipo de tributo
 */
function renderizarDashboardVazio(tipoTributo) {
  // Obter o container do dashboard
  const dashboardContainer = document.getElementById(
    'auditoria-dashboard-container',
  );
  if (!dashboardContainer) {
    return;
  }

  // Limpar o container
  dashboardContainer.innerHTML = '';

  // Mensagem de dashboard vazio
  const emptyMessage = document.createElement('div');
  emptyMessage.className = 'alert alert-info text-center mt-4';
  emptyMessage.innerHTML = `
    <h4>Nenhum dado de auditoria encontrado</h4>
    <p>Não há dados de auditoria para o tributo ${tipoTributo.toUpperCase()} no período selecionado.</p>
    <p>Execute a auditoria para este tributo para visualizar o dashboard.</p>
    <button class="btn btn-primary mt-2" onclick="executarAuditoriaTributo('${tipoTributo}', window.location.pathname.includes('entrada') ? 'entrada' : 'saida')">
      <i class="fas fa-calculator"></i> Executar Auditoria
    </button>
  `;

  dashboardContainer.appendChild(emptyMessage);
}

/**
 * Marca uma inconsistência como vista pelo analista
 * @param {number} resultadoId - ID do resultado de auditoria
 */
function marcarComoVista(resultadoId) {
  // Criar modal para observações
  const modalId = 'modal-marcar-vista';

  // Remover modal existente se houver
  const existingModal = document.getElementById(modalId);
  if (existingModal) {
    existingModal.remove();
  }

  // Criar modal
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = modalId;
  modal.setAttribute('tabindex', '-1');
  modal.innerHTML = `
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas fa-eye"></i>
            Marcar Inconsistência como Analisada
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label for="observacoes-analista" class="form-label">Observações do Analista</label>
            <textarea class="form-control" id="observacoes-analista" rows="4"
                      placeholder="Descreva as providências tomadas ou observações sobre esta inconsistência..."></textarea>
          </div>
          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            Ao marcar como analisada, esta inconsistência será identificada como vista pelo analista.
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="button" class="btn btn-primary" onclick="confirmarMarcacaoVista(${resultadoId})">
            <i class="fas fa-check"></i> Marcar como Analisada
          </button>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Mostrar modal
  const bsModal = new bootstrap.Modal(modal);
  bsModal.show();

  // Remover modal do DOM quando fechado
  modal.addEventListener('hidden.bs.modal', () => {
    modal.remove();
  });
}

/**
 * Confirma a marcação da inconsistência como vista
 * @param {number} resultadoId - ID do resultado de auditoria
 */
function confirmarMarcacaoVista(resultadoId) {
  const observacoes =
    document.getElementById('observacoes-analista')?.value || '';

  // Mostrar loading
  showLoading();

  const url = `http://127.0.0.1:5000/api/auditoria/marcar-vista/${resultadoId}`;

  fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
    body: JSON.stringify({
      observacoes: observacoes,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      // Esconder loading
      hideLoading();

      if (data.success) {
        // Fechar modal
        const modal = document.getElementById('modal-marcar-vista');
        if (modal) {
          const bsModal = bootstrap.Modal.getInstance(modal);
          bsModal.hide();
        }

        // Mostrar mensagem de sucesso
        showSuccessMessage(
          'Inconsistência marcada como analisada com sucesso!',
        );

        // Limpar cache para forçar recarregamento
        limparCacheDetalhamento();

        // Recarregar apenas a tabela de detalhamento
        const tipoTributo = window.location.pathname.split('/').pop();
        const detalhamentoTab = document.getElementById('detalhamento');
        if (detalhamentoTab) {
          detalhamentoTab.removeAttribute('data-loaded');
          carregarDetalhamentoAuditoria(tipoTributo, detalhamentoTab);
        }
      } else {
        // Mostrar mensagem de erro
        const errorMsg =
          data.message || 'Erro ao marcar inconsistência como vista';
        showErrorMessage(errorMsg);
      }
    })
    .catch((error) => {
      // Esconder loading
      hideLoading();

      // Mostrar mensagem de erro
      showErrorMessage(
        `Erro ao marcar inconsistência como vista: ${error.message}`,
      );
    });
}

/**
 * Limpa o cache de detalhamento para forçar recarregamento
 */
function limparCacheDetalhamento() {
  detalhamentoCache.clear();
  filtroOptionsCache.clear();
}

/**
 * Executa a auditoria para um tipo de tributo específico
 * @param {string} tipoTributo - Tipo de tributo (icms, icms-st, ipi, pis, cofins, difal)
 * @param {string} tipoOperacao - Tipo de operação (entrada, saida)
 */
function executarAuditoriaTributo(tipoTributo, tipoOperacao) {
  // Mostrar loading
  showLoading();

  // Obter empresa, ano e mês selecionados
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear') || new Date().getFullYear();
  const month =
    localStorage.getItem('selectedMonth') || new Date().getMonth() + 1;

  if (!empresaId) {
    hideLoading();
    showErrorMessage('Selecione uma empresa para executar a auditoria.');
    return;
  }

  // Construir URL para executar a auditoria
  const url = `http://127.0.0.1:5000/api/auditoria/executar`;

  // Preparar dados para a requisição
  const data = {
    empresa_id: parseInt(empresaId),
    tipo_tributo: tipoTributo,
    tipo_operacao: tipoOperacao === 'entrada' ? 0 : 1, // 0 = entrada, 1 = saída
    year: parseInt(year),
    month: parseInt(month),
    forcar_recalculo: true, // Sempre forçar recálculo para garantir valores corretos
  };

  // Executar a auditoria
  fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
    body: JSON.stringify(data),
  })
    .then((response) => response.json())
    .then((data) => {
      // Esconder loading
      hideLoading();

      if (data.success && data.audit_id) {
        // Iniciar acompanhamento de progresso via WebSocket
        if (window.auditoriaProgressManager) {
          window.auditoriaProgressManager.startAuditProgress(
            data.audit_id,
            0, // Total será atualizado pelo progresso
          );
        } else {
          showSuccessMessage('Auditoria iniciada com sucesso!');

          // Recarregar o dashboard após um tempo
          setTimeout(() => carregarDashboardAuditoria(tipoTributo), 3000);
        }
      } else {
        // Mostrar mensagem de erro
        const errorMsg = data.message || 'Erro ao executar auditoria';
        showErrorMessage(errorMsg);
      }
    })
    .catch((error) => {
      // Esconder loading
      hideLoading();

      // Mostrar mensagem de erro
      showErrorMessage(`Erro ao executar auditoria: ${error.message}`);
    });
}

/**
 * Visualiza as observações do analista em um modal
 * @param {number} resultadoId - ID do resultado da auditoria
 * @param {string} observacoes - Observações do analista
 * @param {string} dataVisualizacao - Data da visualização
 */
function visualizarObservacoes(resultadoId, observacoes, dataVisualizacao) {
  // Criar modal para mostrar observações
  const modalHtml = `
    <div class="modal fade" id="modal-observacoes-analista" tabindex="-1" aria-labelledby="modalObservacoesLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modalObservacoesLabel">
              <i class="fas fa-clipboard-check"></i> Observações da Análise
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="row mb-3">
              <div class="col-12">
                <label class="form-label"><strong>Data da Análise:</strong></label>
                <p class="form-control-plaintext">${dataVisualizacao}</p>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <label class="form-label"><strong>Observações do Analista:</strong></label>
                <div class="border rounded p-3" style="min-height: 100px;">
                  ${observacoes || 'Nenhuma observação registrada.'}
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Remover modal existente se houver
  const existingModal = document.getElementById('modal-observacoes-analista');
  if (existingModal) {
    existingModal.remove();
  }

  // Adicionar modal ao DOM
  document.body.insertAdjacentHTML('beforeend', modalHtml);

  // Mostrar modal
  const modal = new bootstrap.Modal(
    document.getElementById('modal-observacoes-analista'),
  );
  modal.show();

  // Remover modal do DOM quando fechado
  document
    .getElementById('modal-observacoes-analista')
    .addEventListener('hidden.bs.modal', function () {
      this.remove();
    });
}

/**
 * Salva as alterações feitas no cliente no modal de auditoria
 * @param {number} clienteId - ID do cliente
 */
function salvarClienteAuditoria(clienteId) {
  // Obter os valores do formulário
  const atividade = document.getElementById(
    'cliente-atividade-auditoria',
  )?.value;
  const destinacao = document.getElementById(
    'cliente-destinacao-auditoria',
  )?.value;

  // Validar se pelo menos um campo foi preenchido
  if (!atividade && !destinacao) {
    alert('Selecione pelo menos uma atividade ou destinação para atualizar.');
    return;
  }

  // Preparar dados para envio
  const formData = {};
  if (atividade) formData.atividade = atividade;
  if (destinacao) formData.destinacao = destinacao;

  // Mostrar loading no botão
  const btn = document.querySelector(
    `button[onclick*="salvarClienteAuditoria(${clienteId})"]`,
  );
  let originalContent = '';
  if (btn) {
    originalContent = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Salvando...';
    btn.disabled = true;
  }

  // Fazer requisição para atualizar o cliente
  fetch(`http://127.0.0.1:5000/api/clientes/${clienteId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
    body: JSON.stringify(formData),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.message && data.message.includes('sucesso')) {
        // Mostrar mensagem de sucesso
        showSuccessMessage('Cliente atualizado com sucesso!');

        // Fechar o modal após um pequeno delay
        setTimeout(() => {
          const modal = document.getElementById('modal-detalhes-auditoria');
          if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
              bsModal.hide();
            }
          }
        }, 1500);
      } else {
        alert(
          `Erro ao atualizar cliente: ${data.message || 'Erro desconhecido'}`,
        );
      }
    })
    .catch((error) => {
      console.error('Erro ao atualizar cliente:', error);
      alert(`Erro ao atualizar cliente: ${error.message}`);
    })
    .finally(() => {
      // Restaurar botão
      if (btn) {
        btn.innerHTML = originalContent;
        btn.disabled = false;
      }
    });
}

// Inicialização
document.addEventListener('DOMContentLoaded', function () {
  // Verificar se estamos na página de auditoria
  const path = window.location.pathname;
  if (path.match(/\/auditoria\/(entrada|saida)\/([a-z_-]+)/)) {
    // Extrair tipo de tributo da URL
    const tipoTributo = path.split('/').pop();

    // Carregar dashboard
    carregarDashboardAuditoria(tipoTributo);

    // Garantir que os dropdowns estejam fechados se a sidebar estiver colapsada
    setTimeout(() => {
      if (typeof ensureDropdownsClosedWhenCollapsed === 'function') {
        ensureDropdownsClosedWhenCollapsed();
      }
    }, 100);
  }
});
