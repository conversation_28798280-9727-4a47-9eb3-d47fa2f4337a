/**
 * Auth.js - Auditoria Fiscal
 * Funções para autenticação e gerenciamento de usuários
 */

// Variáveis globais
let darkThemeEnabled = false;
let isInitialized = false; // Flag para controlar inicialização única

document.addEventListener('DOMContentLoaded', function () {
  console.log('DOM carregado - auth.js');
  // Verificar se já foi inicializado para evitar duplicação de event listeners
  if (isInitialized) {
    console.log('Auth já inicializado');
    return;
  }

  console.log('Inicializando auth pela primeira vez');
  isInitialized = true;

  // Carregar preferência de tema
  loadThemePreference();

  // Configurar formulário de login
  setupLoginForm();

  // Configurar botão de logout
  setupLogoutButton();

  // Configurar toggle de senha
  setupPasswordToggle();

  // Configurar botão de alternância de tema
  setupThemeToggle();
});

/**
 * Configura o formulário de login
 */
function setupLoginForm() {
  const loginForm = document.getElementById('login-form');
  if (!loginForm) {
    return;
  }

  // Remover event listeners anteriores (se possível)
  const newLoginForm = loginForm.cloneNode(true);
  loginForm.parentNode.replaceChild(newLoginForm, loginForm);

  newLoginForm.addEventListener('submit', function (e) {
    e.preventDefault();

    // Validar formulário
    if (!this.checkValidity()) {
      e.stopPropagation();
      this.classList.add('was-validated');
      return;
    }

    // Obter dados do formulário
    const email = document.getElementById('email').value;
    const senha = document.getElementById('senha').value;
    const loginMsg = document.getElementById('login-msg');

    // Limpar mensagens anteriores
    loginMsg.textContent = '';
    loginMsg.classList.remove('alert-success', 'alert-danger', 'd-none');

    // Mostrar indicador de carregamento
    loginMsg.textContent = 'Autenticando...';
    loginMsg.classList.add('alert-info');
    loginMsg.classList.remove('d-none');

    // Enviar requisição de login
    fetch('/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, senha }),
    })
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        if (data.access_token) {
          // Login bem-sucedido

          // Armazenar o token no localStorage
          localStorage.setItem('token', data.access_token);

          // Verificar se o token foi armazenado corretamente
          const storedToken = localStorage.getItem('token');

          if (storedToken !== data.access_token) {
            console.error('Erro ao armazenar token! Tokens não coincidem!');
          }

          // Armazenar dados do usuário
          localStorage.setItem(
            'currentUser',
            JSON.stringify({
              id: data.usuario_id,
              nome: data.nome,
              email: data.email,
              escritorio_id: data.escritorio_id,
              escritorio_nome: data.escritorio_nome,
              is_admin: data.is_admin,
              tipo_usuario: data.tipo_usuario,
              empresas_permitidas: data.empresas_permitidas,
            }),
          );

          // Exibir mensagem de sucesso
          loginMsg.textContent =
            'Login realizado com sucesso! Redirecionando...';
          loginMsg.classList.add('alert-success');
          loginMsg.classList.remove('alert-info');

          // Redirecionar para a página anterior ou para o dashboard após um breve delay
          const redirectUrl =
            localStorage.getItem('redirectAfterLogin') || '/dashboard';
          setTimeout(() => {
            // Limpar o redirecionamento armazenado
            localStorage.removeItem('redirectAfterLogin');
            window.location.href = redirectUrl;
          }, 1000);
        } else {
          // Login falhou
          console.error(
            'Login falhou:',
            data.message || 'Sem mensagem de erro',
          );
          loginMsg.textContent =
            data.message ||
            'Falha na autenticação. Verifique suas credenciais.';
          loginMsg.classList.add('alert-danger');
          loginMsg.classList.remove('alert-info');
        }
      })
      .catch((error) => {
        console.error('Erro ao processar login:', error);
        loginMsg.textContent =
          'Erro de conexão. Por favor, tente novamente mais tarde.';
        loginMsg.classList.add('alert-danger');
        loginMsg.classList.remove('alert-info');
      });
  });
}

/**
 * Configura o botão de logout
 */
function setupLogoutButton() {
  const logoutBtn = document.getElementById('logout-btn');
  if (!logoutBtn) {
    return;
  }

  // Remover event listeners anteriores (se possível)
  const newLogoutBtn = logoutBtn.cloneNode(true);
  logoutBtn.parentNode.replaceChild(newLogoutBtn, logoutBtn);

  newLogoutBtn.addEventListener('click', function (e) {
    e.preventDefault();
    performLogout();
  });
}

/**
 * Realiza o logout do usuário
 */
function performLogout() {
  // Remover token e dados salvos
  localStorage.removeItem('token');
  localStorage.removeItem('selectedCompany');
  localStorage.removeItem('currentUser');
  localStorage.removeItem('userEscritorioId');

  // Redirecionar para a página de login
  window.location.href = '/web';
}

/**
 * Configura o toggle de visibilidade da senha
 */
function setupPasswordToggle() {
  const toggleButtons = document.querySelectorAll('.toggle-password');

  toggleButtons.forEach((button) => {
    // Remover event listeners anteriores (se possível)
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);

    newButton.addEventListener('click', function () {
      // Obter o input que está antes do botão
      const input = this.previousElementSibling;
      const icon = this.querySelector('i');

      // Alternar tipo de input
      if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }
    });
  });
}

/**
 * Carrega a preferência de tema do usuário
 */
function loadThemePreference() {
  const savedTheme = localStorage.getItem('darkTheme');

  if (savedTheme === 'true') {
    darkThemeEnabled = true;
    document.body.classList.add('dark-theme');

    // Atualizar ícone do botão se ele já existir
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    if (themeToggleBtn) {
      const icon = themeToggleBtn.querySelector('i');
      icon.classList.remove('fa-moon');
      icon.classList.add('fa-sun');
    }
  }
}

/**
 * Configura o botão de alternância de tema
 */
function setupThemeToggle() {
  const themeToggleBtn = document.getElementById('theme-toggle-btn');

  if (themeToggleBtn) {
    // Remover event listeners anteriores (se possível)
    const newThemeToggleBtn = themeToggleBtn.cloneNode(true);
    themeToggleBtn.parentNode.replaceChild(newThemeToggleBtn, themeToggleBtn);

    // Atualizar ícone inicial com base no tema atual
    const icon = newThemeToggleBtn.querySelector('i');
    if (darkThemeEnabled) {
      icon.classList.remove('fa-moon');
      icon.classList.add('fa-sun');
    }

    newThemeToggleBtn.addEventListener('click', function () {
      // Alternar o tema
      darkThemeEnabled = !darkThemeEnabled;

      // Salvar preferência
      localStorage.setItem('darkTheme', darkThemeEnabled);

      // Aplicar ou remover classe do body com uma pequena animação
      if (darkThemeEnabled) {
        document.body.classList.add('dark-theme');
        // Animar o ícone
        icon.style.transform = 'rotate(360deg)';
        setTimeout(() => {
          icon.classList.remove('fa-moon');
          icon.classList.add('fa-sun');
          icon.style.transform = '';
        }, 150);
      } else {
        document.body.classList.remove('dark-theme');
        // Animar o ícone
        icon.style.transform = 'rotate(-360deg)';
        setTimeout(() => {
          icon.classList.remove('fa-sun');
          icon.classList.add('fa-moon');
          icon.style.transform = '';
        }, 150);
      }
    });
  }
}

/**
 * Verifica se o usuário está autenticado
 * @returns {boolean} - True se o usuário estiver autenticado
 */
function isAuthenticated() {
  const token = localStorage.getItem('token');
  return !!token;
}

/**
 * Redireciona para a página de login se o usuário não estiver autenticado
 */
function requireAuth() {
  if (!isAuthenticated()) {
    // Salvar a URL atual para redirecionamento após o login
    localStorage.setItem('redirectAfterLogin', window.location.pathname);
    window.location.href = '/web';
    return false;
  }
  return true;
}
