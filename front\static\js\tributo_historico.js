/**
 * tributo_historico.js - Auditoria Fiscal
 * Funções para gerenciar o histórico de alterações de tributos
 */

/**
 * Renderiza a tabela de histórico de alterações
 * @param {HTMLElement} container - Container onde a tabela será renderizada
 * @param {Array} historico - Lista de registros de histórico
 */
function renderHistoricoTable(container, historico) {
  // Ordenar histórico por data (mais recente primeiro)
  historico.sort((a, b) => {
    return new Date(b.data_alteracao) - new Date(a.data_alteracao);
  });

  // Criar HTML da tabela
  let html = `
    <div class="table-responsive">
      <table id="historico-table" class="table table-striped table-hover">
        <thead>
          <tr>
            <th>Data</th>
            <th>Usuário</th>
            <th>Status Anterior</th>
            <th>Status Novo</th>
            <th>Detalhes</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Adicionar linhas da tabela
  historico.forEach((registro) => {
    const dataAlteracao = new Date(registro.data_alteracao).toLocaleString('pt-BR');
    
    html += `
      <tr>
        <td>${dataAlteracao}</td>
        <td>${registro.usuario || 'N/A'}</td>
        <td><span class="badge ${getStatusBadgeClass(registro.status_anterior)}">${registro.status_anterior || 'N/A'}</span></td>
        <td><span class="badge ${getStatusBadgeClass(registro.status_novo)}">${registro.status_novo || 'N/A'}</span></td>
        <td>
          <button class="btn btn-sm btn-info view-historico-details-btn" data-historico-id="${registro.id}">
            <i class="fas fa-eye"></i>
          </button>
        </td>
      </tr>
    `;
  });

  html += `
        </tbody>
      </table>
    </div>
  `;

  // Atualizar o conteúdo
  container.innerHTML = html;

  // Inicializar DataTable
  try {
    new DataTable('#historico-table', {
      language: {
        url: '/static/js/vendor/datatables/pt-BR.json',
      },
      responsive: true,
      pageLength: 5,
      order: [[0, 'desc']], // Ordenar por data (mais recente primeiro)
    });
  } catch (error) {
    console.error('Erro ao inicializar DataTable:', error);
  }

  // Configurar botões de visualização de detalhes
  document.querySelectorAll('.view-historico-details-btn').forEach((button) => {
    button.addEventListener('click', function () {
      const historicoId = this.dataset.historicoId;
      const registro = historico.find(h => h.id == historicoId);
      if (registro) {
        showHistoricoDetailsModal(registro);
      }
    });
  });
}

/**
 * Mostra o modal com os detalhes do histórico
 * @param {Object} registro - Registro de histórico
 */
function showHistoricoDetailsModal(registro) {
  // Verificar se o modal já existe
  let modalElement = document.getElementById('historico-details-modal');
  if (!modalElement) {
    // Criar o modal
    modalElement = document.createElement('div');
    modalElement.id = 'historico-details-modal';
    modalElement.className = 'modal fade';
    modalElement.tabIndex = -1;
    modalElement.setAttribute('aria-labelledby', 'historico-details-modal-label');
    modalElement.setAttribute('aria-hidden', 'true');

    document.body.appendChild(modalElement);
  }

  // Formatar data
  const dataAlteracao = new Date(registro.data_alteracao).toLocaleString('pt-BR');

  // Preparar comparação de valores
  const comparisonRows = getHistoricoComparisonRows(registro);

  // Conteúdo do modal
  modalElement.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="historico-details-modal-label">Detalhes da Alteração</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
        </div>
        <div class="modal-body">
          <div class="row mb-3">
            <div class="col-md-6">
              <p><strong>Data:</strong> ${dataAlteracao}</p>
              <p><strong>Usuário:</strong> ${registro.usuario || 'N/A'}</p>
            </div>
            <div class="col-md-6">
              <p><strong>Status Anterior:</strong> <span class="badge ${getStatusBadgeClass(registro.status_anterior)}">${registro.status_anterior || 'N/A'}</span></p>
              <p><strong>Status Novo:</strong> <span class="badge ${getStatusBadgeClass(registro.status_novo)}">${registro.status_novo || 'N/A'}</span></p>
            </div>
          </div>
          
          <h6>Valores Alterados</h6>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr class="table-primary">
                  <th>Campo</th>
                  <th>Valor Anterior</th>
                  <th>Valor Novo</th>
                </tr>
              </thead>
              <tbody>
                ${comparisonRows}
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
        </div>
      </div>
    </div>
  `;

  // Mostrar o modal
  const modal = new bootstrap.Modal(modalElement);
  modal.show();
}

/**
 * Obtém as linhas de comparação entre valores anteriores e novos
 * @param {Object} registro - Registro de histórico
 * @returns {string} HTML das linhas de comparação
 */
function getHistoricoComparisonRows(registro) {
  const valoresAnteriores = registro.valores_anteriores || {};
  const valoresNovos = registro.valores_novos || {};
  
  // Obter todos os campos únicos
  const allFields = new Set([
    ...Object.keys(valoresAnteriores),
    ...Object.keys(valoresNovos)
  ]);
  
  // Criar linhas de comparação
  let rows = '';
  
  allFields.forEach(field => {
    const valorAnterior = valoresAnteriores[field];
    const valorNovo = valoresNovos[field];
    
    // Verificar se o valor mudou
    const isDifferent = JSON.stringify(valorAnterior) !== JSON.stringify(valorNovo);
    
    rows += `
      <tr class="${isDifferent ? 'table-warning' : ''}">
        <td>${formatFieldLabel(field)}</td>
        <td>${formatFieldValueForDisplay(valorAnterior)}</td>
        <td>${formatFieldValueForDisplay(valorNovo)}</td>
      </tr>
    `;
  });
  
  return rows;
}

/**
 * Formata o rótulo do campo para exibição
 * @param {string} field - Nome do campo
 * @returns {string} Rótulo formatado
 */
function formatFieldLabel(field) {
  // Converter snake_case para Title Case
  return field
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Formata o valor do campo para exibição
 * @param {*} value - Valor do campo
 * @returns {string} Valor formatado
 */
function formatFieldValueForDisplay(value) {
  if (value === null || value === undefined) {
    return '<em>N/A</em>';
  }
  
  if (typeof value === 'object') {
    return `<pre>${JSON.stringify(value, null, 2)}</pre>`;
  }
  
  if (typeof value === 'number') {
    // Verificar se parece uma porcentagem ou valor monetário
    if (value < 100) {
      return value.toFixed(2) + '%';
    } else {
      return 'R$ ' + value.toFixed(2);
    }
  }
  
  return value.toString();
}
