/**
 * tributo_modal.js - Auditoria Fiscal
 * Funções para gerenciar o modal de detalhes de tributos
 */

/**
 * Mostra o modal com os detalhes do tributo
 * @param {Object} tributo - Dados do tributo
 */
function showTributoModal(tributo) {
  // Verificar se o modal já existe
  let modalElement = document.getElementById('tributo-modal');
  if (!modalElement) {
    // Criar o modal
    modalElement = document.createElement('div');
    modalElement.id = 'tributo-modal';
    modalElement.className = 'modal fade';
    modalElement.tabIndex = -1;
    modalElement.setAttribute('aria-labelledby', 'tributo-modal-label');
    modalElement.setAttribute('aria-hidden', 'true');

    document.body.appendChild(modalElement);
  }

  // Título do tributo
  const titulo = getTituloTributo();

  // Conteúdo do modal com abas
  modalElement.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="tributo-modal-label">Detalhes do ${titulo}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
        </div>
        <div class="modal-body">
          <!-- Abas -->
          <ul class="nav nav-tabs" id="tributo-detail-tabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info-content" type="button" role="tab" aria-controls="info-content" aria-selected="true">
                Informações Gerais
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="cliente-tab" data-bs-toggle="tab" data-bs-target="#cliente-content" type="button" role="tab" aria-controls="cliente-content" aria-selected="false">
                Cliente
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="produto-tab" data-bs-toggle="tab" data-bs-target="#produto-content" type="button" role="tab" aria-controls="produto-content" aria-selected="false">
                Produto
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="tributo-tab" data-bs-toggle="tab" data-bs-target="#tributo-content" type="button" role="tab" aria-controls="tributo-content" aria-selected="false">
                Tributo
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="historico-tab" data-bs-toggle="tab" data-bs-target="#historico-content" type="button" role="tab" aria-controls="historico-content" aria-selected="false">
                Histórico
              </button>
            </li>
          </ul>

          <!-- Conteúdo das abas -->
          <div class="tab-content p-3" id="tributo-detail-content">
            <!-- Aba Informações Gerais -->
            <div class="tab-pane fade show active" id="info-content" role="tabpanel" aria-labelledby="info-tab">
              <div class="row">
                <div class="col-md-6">
                  <h6>Informações da Nota Fiscal</h6>
                  <ul class="list-group mb-3">
                    <li class="list-group-item"><strong>Número NF:</strong> ${
                      tributo.numero_nf || 'N/A'
                    }</li>
                    <li class="list-group-item"><strong>Chave NF:</strong> ${
                      tributo.chave_nf || 'N/A'
                    }</li>
                    <li class="list-group-item"><strong>Tipo de Operação:</strong> ${
                      tributo.tipo_operacao === '0'
                        ? 'Entrada'
                        : tributo.tipo_operacao === '1'
                        ? 'Saída'
                        : 'N/A'
                    }</li>
                    <li class="list-group-item"><strong>Data Emissão:</strong> ${formatDate(
                      tributo.data_emissao,
                    )}</li>
                    ${
                      tributo.data_saida
                        ? `<li class="list-group-item"><strong>Data Saída:</strong> ${formatDate(
                            tributo.data_saida,
                          )}</li>`
                        : ''
                    }
                  </ul>
                </div>
                <div class="col-md-6">
                  <h6>Informações Básicas</h6>
                  <ul class="list-group mb-3">
                    <li class="list-group-item"><strong>Cliente:</strong> ${
                      tributo.cliente?.razao_social || 'N/A'
                    }</li>
                    <li class="list-group-item"><strong>Produto:</strong> ${
                      tributo.produto?.descricao || 'N/A'
                    }</li>
                    <li class="list-group-item"><strong>Status:</strong> <span class="badge ${getStatusBadgeClass(
                      tributo.status,
                    )}">${tributo.status}</span></li>
                    <li class="list-group-item"><strong>Status ${titulo}:</strong> <span class="badge ${getStatusBadgeClass(
    tributo[`${window.cenariosDetalhes.currentTipoTributo}_status`],
  )}">${
    tributo[`${window.cenariosDetalhes.currentTipoTributo}_status`]
  }</span></li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Aba Cliente -->
            <div class="tab-pane fade" id="cliente-content" role="tabpanel" aria-labelledby="cliente-tab">
              ${getClienteTabContent(tributo)}
            </div>

            <!-- Aba Produto -->
            <div class="tab-pane fade" id="produto-content" role="tabpanel" aria-labelledby="produto-tab">
              ${getProdutoTabContent(tributo)}
            </div>

            <!-- Aba Tributo -->
            <div class="tab-pane fade" id="tributo-content" role="tabpanel" aria-labelledby="tributo-tab">
              ${getTributoTabContent(tributo)}
            </div>

            <!-- Aba Histórico -->
            <div class="tab-pane fade" id="historico-content" role="tabpanel" aria-labelledby="historico-tab">
              <div id="historico-loading" class="text-center">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Carregando...</span>
                </div>
                <p>Carregando histórico...</p>
              </div>
              <div id="historico-content-data"></div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          ${
            tributo[`${window.cenariosDetalhes.currentTipoTributo}_status`] ===
              'novo' ||
            tributo[`${window.cenariosDetalhes.currentTipoTributo}_status`] ===
              'inconsistente'
              ? `
            <button type="button" class="btn btn-success" id="modal-send-to-producao-btn" data-tributo-id="${tributo.id}">
              Enviar para Produção
            </button>
          `
              : ''
          }
          <button type="button" class="btn btn-primary" id="modal-save-tributo-btn" data-tributo-id="${
            tributo.id
          }">
            Salvar Alterações
          </button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
        </div>
      </div>
    </div>
  `;

  // Mostrar o modal
  const modal = new bootstrap.Modal(modalElement);
  modal.show();

  // Configurar evento para o botão de enviar para produção
  const sendToProducaoBtn = document.getElementById(
    'modal-send-to-producao-btn',
  );
  if (sendToProducaoBtn) {
    sendToProducaoBtn.addEventListener('click', function () {
      const tributoId = this.dataset.tributoId;
      modal.hide();
      sendToProducao(tributoId);
    });
  }

  // Configurar evento para o botão de salvar alterações
  const saveTributoBtn = document.getElementById('modal-save-tributo-btn');
  if (saveTributoBtn) {
    saveTributoBtn.addEventListener('click', function () {
      const tributoId = this.dataset.tributoId;
      saveTributoChanges(tributoId);
    });
  }

  // Carregar histórico quando a aba for selecionada
  document
    .getElementById('historico-tab')
    .addEventListener('shown.bs.tab', function () {
      loadTributoHistorico(tributo.id);
    });

  // Carregar dados de comparação se for inconsistente
  if (
    tributo[`${window.cenariosDetalhes.currentTipoTributo}_status`] ===
    'inconsistente'
  ) {
    loadProducaoComparisonData(tributo);
  }
}

/**
 * Obtém o conteúdo da aba Cliente
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getClienteTabContent(tributo) {
  const cliente = tributo.cliente || {};

  return `
    <form id="cliente-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-razao-social" class="form-label">Razão Social</label>
            <input type="text" class="form-control" id="cliente-razao-social" value="${
              cliente.razao_social || ''
            }" ${cliente.id ? 'readonly' : ''}>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-cnpj" class="form-label">CNPJ/CPF</label>
            <input type="text" class="form-control" id="cliente-cnpj" value="${
              cliente.cnpj || ''
            }" ${cliente.id ? 'readonly' : ''}>
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-inscricao-estadual" class="form-label">Inscrição Estadual</label>
            <input type="text" class="form-control" id="cliente-inscricao-estadual" value="${
              cliente.inscricao_estadual || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-uf" class="form-label">UF</label>
            <input type="text" class="form-control" id="cliente-uf" value="${
              cliente.uf || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-municipio" class="form-label">Município</label>
            <input type="text" class="form-control" id="cliente-municipio" value="${
              cliente.municipio || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-cnae" class="form-label">CNAE</label>
            <input type="text" class="form-control" id="cliente-cnae" value="${
              cliente.cnae || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-atividade" class="form-label">Atividade</label>
            <select class="form-select" id="cliente-atividade">
              <option value="" ${
                !cliente.atividade ? 'selected' : ''
              }>Selecione...</option>
              <option value="Indústria" ${
                cliente.atividade === 'Indústria' ? 'selected' : ''
              }>Indústria</option>
              <option value="Comércio" ${
                cliente.atividade === 'Comércio' ? 'selected' : ''
              }>Comércio</option>
              <option value="Serviço" ${
                cliente.atividade === 'Serviço' ? 'selected' : ''
              }>Serviço</option>
              <option value="Não Contribuinte" ${
                cliente.atividade === 'Não Contribuinte' ? 'selected' : ''
              }>Não Contribuinte</option>
            </select>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-destinacao" class="form-label">Destinação</label>
            <select class="form-select" id="cliente-destinacao">
              <option value="" ${
                !cliente.destinacao ? 'selected' : ''
              }>Selecione...</option>
              <option value="Revenda" ${
                cliente.destinacao === 'Revenda' ? 'selected' : ''
              }>Revenda</option>
              <option value="Industrialização" ${
                cliente.destinacao === 'Industrialização' ? 'selected' : ''
              }>Industrialização</option>
              <option value="Uso e Consumo" ${
                cliente.destinacao === 'Uso e Consumo' ? 'selected' : ''
              }>Uso e Consumo</option>
              <option value="Ativo Imobilizado" ${
                cliente.destinacao === 'Ativo Imobilizado' ? 'selected' : ''
              }>Ativo Imobilizado</option>
            </select>
          </div>
        </div>
      </div>
      <div class="form-check mb-3">
        <input class="form-check-input" type="checkbox" id="cliente-simples-nacional" ${
          cliente.simples_nacional ? 'checked' : ''
        }>
        <label class="form-check-label" for="cliente-simples-nacional">
          Simples Nacional
        </label>
      </div>
    </form>
  `;
}

/**
 * Obtém o conteúdo da aba Produto
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getProdutoTabContent(tributo) {
  const produto = tributo.produto || {};

  return `
    <form id="produto-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-codigo" class="form-label">Código</label>
            <input type="text" class="form-control" id="produto-codigo" value="${
              produto.codigo || ''
            }" ${produto.id ? 'readonly' : ''}>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-descricao" class="form-label">Descrição</label>
            <input type="text" class="form-control" id="produto-descricao" value="${
              produto.descricao || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-4">
          <div class="mb-3">
            <label for="produto-ncm" class="form-label">NCM</label>
            <input type="text" class="form-control" id="produto-ncm" value="${
              produto.ncm || ''
            }">
          </div>
        </div>
        <div class="col-md-4">
          <div class="mb-3">
            <label for="produto-ex" class="form-label">EX</label>
            <input type="text" class="form-control" id="produto-ex" value="${
              produto.ex || ''
            }">
          </div>
        </div>
        <div class="col-md-4">
          <div class="mb-3">
            <label for="produto-cest" class="form-label">CEST</label>
            <input type="text" class="form-control" id="produto-cest" value="${
              produto.cest || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-cfop" class="form-label">CFOP</label>
            <input type="text" class="form-control" id="produto-cfop" value="${
              produto.cfop || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-unidade-comercial" class="form-label">Unidade Comercial</label>
            <input type="text" class="form-control" id="produto-unidade-comercial" value="${
              produto.unidade_comercial || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-codigo-ean" class="form-label">Código EAN</label>
            <input type="text" class="form-control" id="produto-codigo-ean" value="${
              produto.codigo_ean || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-codigo-ean-tributavel" class="form-label">Código EAN Tributável</label>
            <input type="text" class="form-control" id="produto-codigo-ean-tributavel" value="${
              produto.codigo_ean_tributavel || ''
            }">
          </div>
        </div>
      </div>
    </form>
  `;
}

/**
 * Obtém o conteúdo da aba Tributo
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getTributoTabContent(tributo) {
  // Determinar quais campos mostrar com base no tipo de tributo
  switch (window.cenariosDetalhes.currentTipoTributo) {
    case 'icms':
      return getIcmsTabContent(tributo);
    case 'icms_st':
      return getIcmsStTabContent(tributo);
    case 'ipi':
      return getIpiTabContent(tributo);
    case 'pis':
      return getPisTabContent(tributo);
    case 'cofins':
      return getCofinsTabContent(tributo);
    case 'difal':
      return getDifalTabContent(tributo);
    default:
      return '<div class="alert alert-warning">Tipo de tributo não reconhecido.</div>';
  }
}
