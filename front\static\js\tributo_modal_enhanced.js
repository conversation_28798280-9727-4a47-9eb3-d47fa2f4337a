/**
 * tributo_modal_enhanced.js - Auditoria Fiscal
 * Funções aprimoradas para gerenciar o modal de detalhes de tributos
 */

/**
 * Obtém o conteúdo da aba Cliente
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getClienteTabContent(tributo) {
  const cliente = tributo.cliente || {};

  return `
    <form id="cliente-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-razao-social" class="form-label">Razão Social</label>
            <input type="text" class="form-control" id="cliente-razao-social" value="${
              cliente.razao_social || ''
            }" ${cliente.id ? 'readonly' : ''}>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-cnpj" class="form-label">CNPJ/CPF</label>
            <input type="text" class="form-control" id="cliente-cnpj" value="${
              cliente.cnpj || ''
            }" ${cliente.id ? 'readonly' : ''}>
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-inscricao-estadual" class="form-label">Inscrição Estadual</label>
            <input type="text" class="form-control" id="cliente-inscricao-estadual" value="${
              cliente.inscricao_estadual || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-uf" class="form-label">UF</label>
            <input type="text" class="form-control" id="cliente-uf" value="${
              cliente.uf || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-municipio" class="form-label">Município</label>
            <input type="text" class="form-control" id="cliente-municipio" value="${
              cliente.municipio || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-cnae" class="form-label">CNAE</label>
            <input type="text" class="form-control" id="cliente-cnae" value="${
              cliente.cnae || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-atividade" class="form-label">Atividade</label>
            <select class="form-select" id="cliente-atividade">
              <option value="">Selecione...</option>
              <option value="industria" ${
                cliente.atividade === 'industria' ? 'selected' : ''
              }>Indústria</option>
              <option value="comercio" ${
                cliente.atividade === 'comercio' ? 'selected' : ''
              }>Comércio</option>
              <option value="servico" ${
                cliente.atividade === 'servico' ? 'selected' : ''
              }>Serviço</option>
              <option value="outro" ${
                cliente.atividade === 'outro' ? 'selected' : ''
              }>Outro</option>
            </select>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cliente-destinacao" class="form-label">Destinação</label>
            <select class="form-select" id="cliente-destinacao">
              <option value="">Selecione...</option>
              <option value="consumo" ${
                cliente.destinacao === 'consumo' ? 'selected' : ''
              }>Consumo</option>
              <option value="revenda" ${
                cliente.destinacao === 'revenda' ? 'selected' : ''
              }>Revenda</option>
              <option value="industrializacao" ${
                cliente.destinacao === 'industrializacao' ? 'selected' : ''
              }>Industrialização</option>
              <option value="outro" ${
                cliente.destinacao === 'outro' ? 'selected' : ''
              }>Outro</option>
            </select>
          </div>
        </div>
      </div>
      <div class="form-check mb-3">
        <input class="form-check-input" type="checkbox" id="cliente-simples-nacional" ${
          cliente.simples_nacional ? 'checked' : ''
        }>
        <label class="form-check-label" for="cliente-simples-nacional">
          Simples Nacional
        </label>
      </div>
    </form>
  `;
}

/**
 * Obtém o conteúdo da aba Produto
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getProdutoTabContent(tributo) {
  const produto = tributo.produto || {};

  return `
    <form id="produto-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-codigo" class="form-label">Código</label>
            <input type="text" class="form-control" id="produto-codigo" value="${
              produto.codigo || ''
            }" ${produto.id ? 'readonly' : ''}>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-descricao" class="form-label">Descrição</label>
            <input type="text" class="form-control" id="produto-descricao" value="${
              produto.descricao || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-ncm" class="form-label">NCM</label>
            <input type="text" class="form-control" id="produto-ncm" value="${
              produto.ncm || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-cfop" class="form-label">CFOP</label>
            <input type="text" class="form-control" id="produto-cfop" value="${
              produto.cfop || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-unidade" class="form-label">Unidade</label>
            <input type="text" class="form-control" id="produto-unidade" value="${
              produto.unidade || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-cean" class="form-label">CEAN</label>
            <input type="text" class="form-control" id="produto-cean" value="${
              produto.cean || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-cest" class="form-label">CEST</label>
            <input type="text" class="form-control" id="produto-cest" value="${
              produto.cest || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="produto-ex" class="form-label">EX</label>
            <input type="text" class="form-control" id="produto-ex" value="${
              produto.ex || ''
            }">
          </div>
        </div>
      </div>
    </form>
  `;
}

/**
 * Obtém o conteúdo da aba Tributo
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getTributoTabContent(tributo) {
  // Determinar quais campos mostrar com base no tipo de tributo
  switch (window.cenariosDetalhes.currentTipoTributo) {
    case 'icms':
      return getIcmsTabContent(tributo);
    case 'icms_st':
      return getIcmsStTabContent(tributo);
    case 'ipi':
      return getIpiTabContent(tributo);
    case 'pis':
      return getPisTabContent(tributo);
    case 'cofins':
      return getCofinsTabContent(tributo);
    case 'difal':
      return getDifalTabContent(tributo);
    default:
      return '<div class="alert alert-warning">Tipo de tributo não reconhecido.</div>';
  }
}

/**
 * Obtém o conteúdo da aba para ICMS
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getIcmsTabContent(tributo) {
  return `
    <form id="tributo-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="icms-cst" value="${
              tributo.icms_cst || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-origem" class="form-label">Origem</label>
            <input type="text" class="form-control" id="icms-origem" value="${
              tributo.icms_origem || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-mod-bc" class="form-label">Modalidade BC</label>
            <input type="text" class="form-control" id="icms-mod-bc" value="${
              tributo.icms_mod_bc || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-p-red-bc" class="form-label">Redução BC (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms-p-red-bc" value="${
              tributo.icms_p_red_bc || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-vbc" class="form-label">Base de Cálculo</label>
            <input type="number" step="0.01" class="form-control" id="icms-vbc" value="${
              tributo.icms_vbc || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms-aliquota" value="${
              tributo.icms_aliquota || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-valor" class="form-label">Valor</label>
            <input type="number" step="0.01" class="form-control" id="icms-valor" value="${
              tributo.icms_valor || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-v-op" class="form-label">Valor Operação</label>
            <input type="number" step="0.01" class="form-control" id="icms-v-op" value="${
              tributo.icms_v_op || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-p-dif" class="form-label">Percentual Diferimento</label>
            <input type="number" step="0.01" class="form-control" id="icms-p-dif" value="${
              tributo.icms_p_dif || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-v-dif" class="form-label">Valor Diferimento</label>
            <input type="number" step="0.01" class="form-control" id="icms-v-dif" value="${
              tributo.icms_v_dif || ''
            }">
          </div>
        </div>
      </div>
    </form>
  `;
}

/**
 * Obtém o conteúdo da aba para ICMS-ST
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getIcmsStTabContent(tributo) {
  return `
    <form id="tributo-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-st-mod-bc" class="form-label">Modalidade BC</label>
            <input type="text" class="form-control" id="icms-st-mod-bc" value="${
              tributo.icms_st_mod_bc || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-st-p-mva" class="form-label">MVA (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms-st-p-mva" value="${
              tributo.icms_st_p_mva || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-st-vbc" class="form-label">Base de Cálculo</label>
            <input type="number" step="0.01" class="form-control" id="icms-st-vbc" value="${
              tributo.icms_st_vbc || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-st-aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms-st-aliquota" value="${
              tributo.icms_st_aliquota || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-st-valor" class="form-label">Valor</label>
            <input type="number" step="0.01" class="form-control" id="icms-st-valor" value="${
              tributo.icms_st_valor || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-origem" class="form-label">ICMS Origem</label>
            <input type="text" class="form-control" id="icms-origem" value="${
              tributo.icms_origem || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-cst" class="form-label">ICMS CST</label>
            <input type="text" class="form-control" id="icms-cst" value="${
              tributo.icms_cst || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-aliquota" class="form-label">ICMS Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms-aliquota" value="${
              tributo.icms_aliquota || ''
            }">
          </div>
        </div>
      </div>
    </form>
  `;
}

/**
 * Obtém o conteúdo da aba para IPI
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getIpiTabContent(tributo) {
  return `
    <form id="tributo-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="ipi-cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="ipi-cst" value="${
              tributo.ipi_cst || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="ipi-codigo-enquadramento" class="form-label">Código Enquadramento</label>
            <input type="text" class="form-control" id="ipi-codigo-enquadramento" value="${
              tributo.ipi_codigo_enquadramento || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="ipi-vbc" class="form-label">Base de Cálculo</label>
            <input type="number" step="0.01" class="form-control" id="ipi-vbc" value="${
              tributo.ipi_vbc || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="ipi-aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="ipi-aliquota" value="${
              tributo.ipi_aliquota || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="ipi-valor" class="form-label">Valor</label>
            <input type="number" step="0.01" class="form-control" id="ipi-valor" value="${
              tributo.ipi_valor || ''
            }">
          </div>
        </div>
      </div>
    </form>
  `;
}

/**
 * Obtém o conteúdo da aba para PIS
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getPisTabContent(tributo) {
  return `
    <form id="tributo-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="pis-cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="pis-cst" value="${
              tributo.pis_cst || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="pis-vbc" class="form-label">Base de Cálculo</label>
            <input type="number" step="0.01" class="form-control" id="pis-vbc" value="${
              tributo.pis_vbc || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="pis-aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="pis-aliquota" value="${
              tributo.pis_aliquota || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="pis-valor" class="form-label">Valor</label>
            <input type="number" step="0.01" class="form-control" id="pis-valor" value="${
              tributo.pis_valor || ''
            }">
          </div>
        </div>
      </div>
    </form>
  `;
}

/**
 * Obtém o conteúdo da aba para COFINS
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getCofinsTabContent(tributo) {
  return `
    <form id="tributo-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cofins-cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="cofins-cst" value="${
              tributo.cofins_cst || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cofins-vbc" class="form-label">Base de Cálculo</label>
            <input type="number" step="0.01" class="form-control" id="cofins-vbc" value="${
              tributo.cofins_vbc || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cofins-aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="cofins-aliquota" value="${
              tributo.cofins_aliquota || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="cofins-valor" class="form-label">Valor</label>
            <input type="number" step="0.01" class="form-control" id="cofins-valor" value="${
              tributo.cofins_valor || ''
            }">
          </div>
        </div>
      </div>
    </form>
  `;
}

/**
 * Obtém o conteúdo da aba para DIFAL
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getDifalTabContent(tributo) {
  return `
    <form id="tributo-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="difal-vbc" class="form-label">Base de Cálculo</label>
            <input type="number" step="0.01" class="form-control" id="difal-vbc" value="${
              tributo.difal_vbc || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="difal-p-fcp-uf-dest" class="form-label">FCP UF Destino (%)</label>
            <input type="number" step="0.01" class="form-control" id="difal-p-fcp-uf-dest" value="${
              tributo.difal_p_fcp_uf_dest || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="difal-p-icms-uf-dest" class="form-label">ICMS UF Destino (%)</label>
            <input type="number" step="0.01" class="form-control" id="difal-p-icms-uf-dest" value="${
              tributo.difal_p_icms_uf_dest || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="difal-p-icms-inter" class="form-label">ICMS Interestadual (%)</label>
            <input type="number" step="0.01" class="form-control" id="difal-p-icms-inter" value="${
              tributo.difal_p_icms_inter || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="difal-v-fcp-uf-dest" class="form-label">Valor FCP UF Destino</label>
            <input type="number" step="0.01" class="form-control" id="difal-v-fcp-uf-dest" value="${
              tributo.difal_v_fcp_uf_dest || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="difal-v-icms-uf-dest" class="form-label">Valor ICMS UF Destino</label>
            <input type="number" step="0.01" class="form-control" id="difal-v-icms-uf-dest" value="${
              tributo.difal_v_icms_uf_dest || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="difal-v-icms-uf-remet" class="form-label">Valor ICMS UF Remetente</label>
            <input type="number" step="0.01" class="form-control" id="difal-v-icms-uf-remet" value="${
              tributo.difal_v_icms_uf_remet || ''
            }">
          </div>
        </div>
      </div>
    </form>
  `;
}

/**
 * Mostra o modal com os detalhes do tributo
 * @param {Object} tributo - Dados do tributo
 */
function showTributoModal(tributo) {
  // Verificar se o modal já existe
  let modalElement = document.getElementById('tributo-modal');
  if (!modalElement) {
    // Criar o modal
    modalElement = document.createElement('div');
    modalElement.id = 'tributo-modal';
    modalElement.className = 'modal fade';
    modalElement.tabIndex = -1;
    modalElement.setAttribute('aria-labelledby', 'tributo-modal-label');
    modalElement.setAttribute('aria-hidden', 'true');

    document.body.appendChild(modalElement);
  }

  // Título do tributo
  const titulo = getTituloTributo();

  // Status atual do tributo
  const currentStatus =
    tributo[`${window.cenariosDetalhes.currentTipoTributo}_status`];

  // Conteúdo do modal com abas
  modalElement.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="tributo-modal-label">Detalhes do ${titulo}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
        </div>
        <div class="modal-body">
          <!-- Abas -->
          <ul class="nav nav-tabs" id="tributo-detail-tabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info-content" type="button" role="tab" aria-controls="info-content" aria-selected="true">
                Informações Gerais
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="cliente-tab" data-bs-toggle="tab" data-bs-target="#cliente-content" type="button" role="tab" aria-controls="cliente-content" aria-selected="false">
                Cliente
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="produto-tab" data-bs-toggle="tab" data-bs-target="#produto-content" type="button" role="tab" aria-controls="produto-content" aria-selected="false">
                Produto
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="tributo-tab" data-bs-toggle="tab" data-bs-target="#tributo-content" type="button" role="tab" aria-controls="tributo-content" aria-selected="false">
                Tributo
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="historico-tab" data-bs-toggle="tab" data-bs-target="#historico-content" type="button" role="tab" aria-controls="historico-content" aria-selected="false">
                Histórico
              </button>
            </li>
          </ul>

          <!-- Conteúdo das abas -->
          <div class="tab-content p-3" id="tributo-detail-content">
            <!-- Aba Informações Gerais -->
            <div class="tab-pane fade show active" id="info-content" role="tabpanel" aria-labelledby="info-tab">
              <div class="row">
                <div class="col-md-6">
                  <h6>Informações da Nota Fiscal</h6>
                  <ul class="list-group mb-3">
                    <li class="list-group-item"><strong>Número NF:</strong> ${
                      tributo.numero_nf || 'N/A'
                    }</li>
                    <li class="list-group-item"><strong>Chave NF:</strong> ${
                      tributo.chave_nf || 'N/A'
                    }</li>
                    <li class="list-group-item"><strong>Tipo de Operação:</strong> ${
                      tributo.tipo_operacao === '0'
                        ? 'Entrada'
                        : tributo.tipo_operacao === '1'
                        ? 'Saída'
                        : 'N/A'
                    }</li>
                    <li class="list-group-item"><strong>Data Emissão:</strong> ${formatDate(
                      tributo.data_emissao,
                    )}</li>
                    ${
                      tributo.data_saida
                        ? `<li class="list-group-item"><strong>Data Saída:</strong> ${formatDate(
                            tributo.data_saida,
                          )}</li>`
                        : ''
                    }
                  </ul>
                </div>
                <div class="col-md-6">
                  <h6>Informações Básicas</h6>
                  <ul class="list-group mb-3">
                    <li class="list-group-item"><strong>Cliente:</strong> ${
                      tributo.cliente?.razao_social || 'N/A'
                    }</li>
                    <li class="list-group-item"><strong>Produto:</strong> ${
                      tributo.produto?.descricao || 'N/A'
                    }</li>
                    <li class="list-group-item"><strong>Status:</strong> <span class="badge ${getStatusBadgeClass(
                      tributo.status,
                    )}">${tributo.status}</span></li>
                    <li class="list-group-item"><strong>Status ${titulo}:</strong> <span class="badge ${getStatusBadgeClass(
    currentStatus,
  )}">${currentStatus}</span></li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Aba Cliente -->
            <div class="tab-pane fade" id="cliente-content" role="tabpanel" aria-labelledby="cliente-tab">
              ${getClienteTabContent(tributo)}
            </div>

            <!-- Aba Produto -->
            <div class="tab-pane fade" id="produto-content" role="tabpanel" aria-labelledby="produto-tab">
              ${getProdutoTabContent(tributo)}
            </div>

            <!-- Aba Tributo -->
            <div class="tab-pane fade" id="tributo-content" role="tabpanel" aria-labelledby="tributo-tab">
              ${getTributoTabContent(tributo)}
            </div>

            <!-- Aba Histórico -->
            <div class="tab-pane fade" id="historico-content" role="tabpanel" aria-labelledby="historico-tab">
              <div id="historico-loading" class="text-center">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Carregando...</span>
                </div>
                <p>Carregando histórico...</p>
              </div>
              <div id="historico-content-data"></div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          ${
            currentStatus === 'novo' || currentStatus === 'inconsistente'
              ? `
            <button type="button" class="btn btn-success" id="modal-send-to-producao-btn" data-tributo-id="${tributo.id}">
              Enviar para Produção
            </button>
          `
              : ''
          }
          ${
            currentStatus === 'producao' || currentStatus === 'inconsistente'
              ? `
            <button type="button" class="btn btn-info" id="modal-mark-as-conforme-btn" data-tributo-id="${tributo.id}">
              Marcar como Conforme
            </button>
          `
              : ''
          }
          <button type="button" class="btn btn-primary" id="modal-save-tributo-btn" data-tributo-id="${
            tributo.id
          }">
            Salvar Alterações
          </button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
        </div>
      </div>
    </div>
  `;

  // Mostrar o modal
  const modal = new bootstrap.Modal(modalElement);
  modal.show();

  // Configurar evento para o botão de enviar para produção
  const sendToProducaoBtn = document.getElementById(
    'modal-send-to-producao-btn',
  );
  if (sendToProducaoBtn) {
    sendToProducaoBtn.addEventListener('click', function () {
      const tributoId = this.dataset.tributoId;
      modal.hide();
      updateTributoStatus(tributoId, 'producao');
    });
  }

  // Configurar evento para o botão de marcar como conforme
  const markAsConformeBtn = document.getElementById(
    'modal-mark-as-conforme-btn',
  );
  if (markAsConformeBtn) {
    markAsConformeBtn.addEventListener('click', function () {
      const tributoId = this.dataset.tributoId;
      if (confirm('Deseja realmente marcar este tributo como conforme?')) {
        modal.hide();
        updateTributoStatus(tributoId, 'conforme');
      }
    });
  }

  // Configurar evento para o botão de salvar alterações
  const saveTributoBtn = document.getElementById('modal-save-tributo-btn');
  if (saveTributoBtn) {
    saveTributoBtn.addEventListener('click', function () {
      const tributoId = this.dataset.tributoId;
      saveTributoChanges(tributoId);
    });
  }

  // Carregar histórico quando a aba for selecionada
  const historicoTab = document.getElementById('historico-tab');
  if (historicoTab) {
    historicoTab.addEventListener('shown.bs.tab', function () {
      loadTributoHistorico(tributo.id);
    });
  }

  // Carregar dados de comparação se for inconsistente
  if (currentStatus === 'inconsistente') {
    loadProducaoComparisonData(tributo);
  }
}

/**
 * Atualiza o status de um tributo
 * @param {number} tributoId - ID do tributo
 * @param {string} newStatus - Novo status para o tributo (default: 'producao')
 */
function updateTributoStatus(tributoId, newStatus = 'producao') {
  // Usar a função do arquivo cenarios_detalhes.js para manter a consistência
  if (
    typeof window.cenariosDetalhes !== 'undefined' &&
    typeof window.cenariosDetalhes.updateTributoStatus === 'function'
  ) {
    // Se a função global estiver disponível, usá-la
    window.cenariosDetalhes.updateTributoStatus(tributoId, newStatus);
  } else {
    // Fallback para a implementação local
    const statusText = newStatus === 'producao' ? 'produção' : newStatus;
    if (!confirm(`Deseja realmente enviar este tributo para ${statusText}?`)) {
      return;
    }

    // Fazer requisição para atualizar o status do tributo
    fetch(`/api/tributos/${tributoId}/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify({
        tipo_tributo: window.cenariosDetalhes.currentTipoTributo,
        status: newStatus,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.message) {
          alert(data.message);
          // Recarregar os dados para todas as abas
          loadTributoData('novo');
          loadTributoData('producao');
          loadTributoData('inconsistente');
        } else {
          alert('Erro ao atualizar status do tributo.');
        }
      })
      .catch((error) => {
        console.error('Erro ao atualizar status do tributo:', error);
        alert('Erro ao atualizar status do tributo.');
      });
  }
}
