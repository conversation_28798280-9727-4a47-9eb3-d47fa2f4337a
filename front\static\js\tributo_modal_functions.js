/**
 * tributo_modal_functions.js - Auditoria Fiscal
 * Funções auxiliares para o modal de detalhes de tributos
 */

/**
 * Obtém o conteúdo da aba para ICMS
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getIcmsTabContent(tributo) {
  return `
    <form id="tributo-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="icms-cst" value="${
              tributo.icms_cst || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-origem" class="form-label">Origem</label>
            <input type="text" class="form-control" id="icms-origem" value="${
              tributo.icms_origem || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms-aliquota" value="${
              tributo.icms_aliquota || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-p-red-bc" class="form-label">Redução BC (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms-p-red-bc" value="${
              tributo.icms_p_red_bc || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-vbc" class="form-label">Base de Cálculo</label>
            <input type="number" step="0.01" class="form-control" id="icms-vbc" value="${
              tributo.icms_vbc || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-valor" class="form-label">Valor</label>
            <input type="number" step="0.01" class="form-control" id="icms-valor" value="${
              tributo.icms_valor || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-4">
          <div class="mb-3">
            <label for="icms-v-op" class="form-label">Valor Operação</label>
            <input type="number" step="0.01" class="form-control" id="icms-v-op" value="${
              tributo.icms_v_op || ''
            }">
          </div>
        </div>
        <div class="col-md-4">
          <div class="mb-3">
            <label for="icms-p-dif" class="form-label">Percentual Diferimento</label>
            <input type="number" step="0.01" class="form-control" id="icms-p-dif" value="${
              tributo.icms_p_dif || ''
            }">
          </div>
        </div>
        <div class="col-md-4">
          <div class="mb-3">
            <label for="icms-v-dif" class="form-label">Valor Diferimento</label>
            <input type="number" step="0.01" class="form-control" id="icms-v-dif" value="${
              tributo.icms_v_dif || ''
            }">
          </div>
        </div>
      </div>
    </form>
  `;
}

/**
 * Obtém o conteúdo da aba para ICMS-ST
 * @param {Object} tributo - Dados do tributo
 * @returns {string} HTML do conteúdo da aba
 */
function getIcmsStTabContent(tributo) {
  return `
    <form id="tributo-form">
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-st-mod-bc" class="form-label">Modalidade BC</label>
            <input type="text" class="form-control" id="icms-st-mod-bc" value="${
              tributo.icms_st_mod_bc || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-st-p-mva" class="form-label">MVA (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms-st-p-mva" value="${
              tributo.icms_st_p_mva || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-st-vbc" class="form-label">Base de Cálculo</label>
            <input type="number" step="0.01" class="form-control" id="icms-st-vbc" value="${
              tributo.icms_st_vbc || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-st-aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms-st-aliquota" value="${
              tributo.icms_st_aliquota || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-st-valor" class="form-label">Valor</label>
            <input type="number" step="0.01" class="form-control" id="icms-st-valor" value="${
              tributo.icms_st_valor || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-st-status" class="form-label">Status</label>
        </div>
      </div>
      <h6 class="mt-4">Informações de ICMS</h6>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="icms-cst" value="${
              tributo.icms_cst || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-origem" class="form-label">Origem</label>
            <input type="text" class="form-control" id="icms-origem" value="${
              tributo.icms_origem || ''
            }">
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms-aliquota" value="${
              tributo.icms_aliquota || ''
            }">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="icms-p-red-bc" class="form-label">Redução BC (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms-p-red-bc" value="${
              tributo.icms_p_red_bc || ''
            }">
          </div>
        </div>
      </div>
    </form>
  `;
}
